/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import setup from '../../setup';
import { expect } from 'chai';
import { createSandbox, SinonStub, SinonStubbedInstance } from 'sinon';
import { ObjectId } from 'bson';
import {
  getReportDocumentManager,
  ReportDocumentManager,
} from '../../../server/service/report-document/ReportDocumentManager';
import ReportDocument, { CreateReportDocument } from '../../../server/models/reportDocument';
import { testLogger } from '../../factories/logger';
import { ReportingFactory } from '../../../server/service/reporting/ReportingFactory';
import { wwgLogger } from '../../../server/service/wwgLogger';
import {
  getCreateReportDocumentFixture,
  getEditorStateFixture,
  getReportDocumentFixture,
} from '../../fixtures/reportDocumentFixtures';
import { userOne } from '../../fixtures/userFixtures';

describe('ReportDocumentManager', () => {
  const sandbox = createSandbox();
  let manager: ReportDocumentManager;
  let reportingFactoryStub: SinonStubbedInstance<ReportingFactory>;
  let generatorStub: {
    getTemplate: SinonStub;
    downloadReport: SinonStub;
  };

  beforeEach(() => {
    generatorStub = {
      getTemplate: sandbox.stub(),
      downloadReport: sandbox.stub(),
    };
    const factory = {
      getLexicalStateGenerator: sandbox.stub().returns(generatorStub),
    };
    reportingFactoryStub = {
      getFactory: sandbox.stub().returns(factory),
    } as any;

    manager = new ReportDocumentManager(testLogger, reportingFactoryStub);
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getReportDocumentManager()).to.be.instanceOf(ReportDocumentManager);
  });

  it('should create instance with default reporting factory', () => {
    const defaultManager = new ReportDocumentManager(testLogger, new ReportingFactory(wwgLogger));
    expect(defaultManager['reportingFactory']).to.be.instanceOf(ReportingFactory);
  });

  describe('create', () => {
    let saveStub: SinonStub;

    beforeEach(() => {
      saveStub = setup.wrapStub(sandbox, ReportDocument.prototype, 'save', function (this: CreateReportDocument) {
        return this;
      });
    });

    const createData = getCreateReportDocumentFixture();

    it('should create a new report document', async () => {
      const document = await manager.create(createData);
      expect(document.title).equals(createData.title);
      expect(saveStub.calledOnce).to.be.true;
    });

    it('should throw error if save fails', async () => {
      const error = new Error('Save failed');
      saveStub.rejects(error);
      await expect(manager.create(createData)).to.be.rejectedWith(error);
    });
  });

  describe('get', () => {
    let findOneStub: SinonStub;
    const reportId = new ObjectId();
    const initiativeId = new ObjectId();

    beforeEach(() => {
      findOneStub = setup.wrapStub(sandbox, ReportDocument, 'findOne', () => ({
        orFail: () => ({
          lean: () => Promise.resolve({ _id: reportId, initiativeId }),
        }),
      }));
    });

    it('should get a report by id', async () => {
      const report = await manager.get({ reportId: reportId.toString(), initiativeId: initiativeId.toString() });
      expect(findOneStub.calledOnce).to.be.true;
      expect(findOneStub.firstCall.args[0]).to.deep.equal({
        _id: reportId,
        initiativeId,
      });
      expect(report).to.deep.equal({ _id: reportId, initiativeId });
    });

    it('should throw an error if getting a report fails', async () => {
      const error = new Error('Get failed');
      findOneStub.returns({
        orFail: () => ({
          lean: () => Promise.reject(error),
        }),
      });
      await expect(
        manager.get({ reportId: reportId.toString(), initiativeId: initiativeId.toString() })
      ).to.be.rejectedWith(error);
    });
  });

  describe('list', () => {
    let findStub: SinonStub;
    const initiativeId = new ObjectId();

    beforeEach(() => {
      findStub = setup.wrapStub(sandbox, ReportDocument, 'find', () => ({
        lean: () => Promise.resolve([getReportDocumentFixture(new ObjectId(), initiativeId)]),
      }));
    });

    it('should list reports by initiativeId', async () => {
      const reports = await manager.list({ initiativeId: initiativeId.toString() });
      expect(findStub.calledOnce).to.be.true;
      expect(findStub.firstCall.args[0]).to.deep.equal({
        initiativeId,
      });
      expect(reports).to.be.an('array').with.lengthOf(1);
    });

    it('should throw an error if listing reports fails', async () => {
      const error = new Error('List failed');
      findStub.returns({
        lean: () => Promise.reject(error),
      });
      await expect(manager.list({ initiativeId: initiativeId.toString() })).to.be.rejectedWith(error);
    });

    it('should return an empty array if no reports are found', async () => {
      findStub.returns({
        lean: () => Promise.resolve([]),
      });
      const reports = await manager.list({ initiativeId: initiativeId.toString() });
      expect(findStub.calledOnce).to.be.true;
      expect(reports).to.be.an('array').with.lengthOf(0);
    });
  });

  describe('update', () => {
    let findOneAndUpdateStub: SinonStub;
    const reportId = new ObjectId();
    const initiativeId = new ObjectId();
    const updatePayload = { title: 'Updated Title' };

    beforeEach(() => {
      findOneAndUpdateStub = setup.wrapStub(sandbox, ReportDocument, 'findOneAndUpdate', () => ({
        orFail: () => ({
          exec: () => Promise.resolve({ _id: reportId, initiativeId, ...updatePayload }),
        }),
      }));
    });

    it('should update a report', async () => {
      const report = await manager.update({
        reportId: reportId.toString(),
        initiativeId: initiativeId.toString(),
        ...updatePayload,
      });
      expect(findOneAndUpdateStub.calledOnce).to.be.true;
      expect(findOneAndUpdateStub.firstCall.args[0]).to.deep.equal({
        _id: reportId,
        initiativeId,
      });
      expect(findOneAndUpdateStub.firstCall.args[1]).to.deep.equal({
        $set: updatePayload,
      });
      expect(report.title).to.equal('Updated Title');
    });

    it('should throw an error if updating a report fails', async () => {
      const error = new Error('Update failed');
      findOneAndUpdateStub.returns({
        orFail: () => ({
          exec: () => Promise.reject(error),
        }),
      });
      await expect(
        manager.update({
          reportId: reportId.toString(),
          initiativeId: initiativeId.toString(),
          ...updatePayload,
        })
      ).to.be.rejectedWith(error);
    });
  });

  describe('deleteReport', () => {
    let findOneAndDeleteStub: SinonStub;

    beforeEach(() => {
      findOneAndDeleteStub = setup.wrapStub(sandbox, ReportDocument, 'findOneAndDelete', () => ({
        orFail: () => ({ exec: () => Promise.resolve({ _id: new ObjectId() }) }),
      }));
    });

    it('should delete a report document', async () => {
      const reportId = new ObjectId().toString();
      const initiativeId = new ObjectId().toString();
      await manager.deleteReport({ reportId, initiativeId });

      expect(findOneAndDeleteStub.calledOnce).to.be.true;
      expect(findOneAndDeleteStub.firstCall.args[0]).to.deep.equal({
        initiativeId: new ObjectId(initiativeId),
        _id: new ObjectId(reportId),
      });
    });

    it('should throw error if delete fails', async () => {
      const error = new Error('Delete failed');
      findOneAndDeleteStub.returns({
        orFail: () => ({ exec: () => Promise.reject(error) }),
      });

      const reportId = new ObjectId().toString();
      const initiativeId = new ObjectId().toString();

      await expect(manager.deleteReport({ reportId, initiativeId })).to.be.rejectedWith(error);
    });
  });

  describe('getTemplate', () => {
    let findByIdStub: SinonStub;
    const reportId = new ObjectId();
    const user = userOne;
    const reportDocument = getReportDocumentFixture(reportId);

    beforeEach(() => {
      findByIdStub = setup.wrapStub(sandbox, ReportDocument, 'findById', () => ({
        orFail: () => ({
          lean: () => Promise.resolve(reportDocument),
        }),
      }));
      generatorStub.getTemplate.resolves({ template: 'mockTemplate' });
    });

    it('should return the template', async () => {
      const result = await manager.getTemplate({ reportId: reportId.toString(), user });

      expect(findByIdStub.calledOnceWith(new ObjectId(reportId.toString()))).to.be.true;
      expect(reportingFactoryStub.getFactory.calledOnceWith(reportDocument.type)).to.be.true;
      expect(generatorStub.getTemplate.calledOnce).to.be.true;
      expect(result).to.deep.equal({ template: 'mockTemplate' });
    });

    it('should throw error if report document not found', async () => {
      findByIdStub.returns({
        orFail: () => ({
          lean: () => Promise.reject(new Error('Report not found')),
        }),
      });
      await expect(manager.getTemplate({ reportId: reportId.toString(), user })).to.be.rejectedWith('Report not found');
    });
  });

  describe('download', () => {
    let findByIdStub: SinonStub;
    const reportId = new ObjectId();
    const editorState = getEditorStateFixture();
    const reportDocument = getReportDocumentFixture(reportId);

    beforeEach(() => {
      findByIdStub = setup.wrapStub(sandbox, ReportDocument, 'findById', () => ({
        orFail: () => ({
          lean: () => Promise.resolve(reportDocument),
        }),
      }));
      generatorStub.downloadReport.resolves('mockDownload');
    });

    it('should download the report', async () => {
      const result = await manager.download({ reportId: reportId.toString(), editorState });

      expect(findByIdStub.calledOnceWith(new ObjectId(reportId.toString()))).to.be.true;
      expect(reportingFactoryStub.getFactory.calledOnceWith(reportDocument.type)).to.be.true;
      expect(generatorStub.downloadReport.calledOnce).to.be.true;
      expect(result).to.equal('mockDownload');
    });

    it('should throw error if report document not found', async () => {
      findByIdStub.returns({
        orFail: () => ({
          lean: () => Promise.reject(new Error('Report not found')),
        }),
      });
      await expect(manager.download({ reportId: reportId.toString(), editorState })).to.be.rejectedWith(
        'Report not found'
      );
    });
  });
});

import { create<PERSON>and<PERSON>, SinonStub } from 'sinon';
import { expect } from 'chai';
import { AppIntegrationService } from '../../../server/service/app-integration/AppIntegrationService';
import UserError from '../../../server/error/UserError';
import '../../setup';
import { initiativeCTPro, initiativeMaterialityTracker, initiativeStarter } from '../../fixtures/initiativeFixtures';
import { userOne } from '../../fixtures/userFixtures';
import { ProductCodes, Subscription } from '../../../server/models/customer';
import { defaultCTPro } from '../../../server/service/app/company-tracker/defaultCTPro';
import { defaultMT } from '../../../server/service/app/materiality-tracker';
import { getCustomerManager } from '../../../server/service/payment/CustomerManager';
import { InitiativeModel } from '../../../server/models/initiative';
import { getSelfOnboardingManager } from '../../../server/service/onboarding/SelfOnboardingManager';
import { getMaterialityMetricGroupService } from '../../../server/service/materiality-assessment/MaterialityMetricGroupService';
import { getUTCEndOf } from '../../../server/util/date';
import { DataPeriods } from '../../../server/service/utr/constants';
import { ObjectId } from 'bson';

describe('AppIntegrationService', () => {
  let service: AppIntegrationService;
  const customerManager = getCustomerManager();
  const selfOnboardingManager = getSelfOnboardingManager();
  const MTMetricGroupService = getMaterialityMetricGroupService();

  const sandbox = createSandbox();

  beforeEach(() => {
    service = new AppIntegrationService(customerManager, selfOnboardingManager, MTMetricGroupService);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('addCompanyTracker', () => {
    it('should throw error if initiative is not valid materiality tracker', async () => {
      await expect(
        service.addCompanyTracker({
          initiative: initiativeStarter as unknown as InitiativeModel,
          user: userOne,
        })
      ).to.eventually.be.rejectedWith(UserError, 'Your organization is not valid Materiality Tracker');
    });

    it('should throw error if initiative connected CT before', async () => {
      sandbox.stub(customerManager, 'getSubscriptions').resolves([
        {
          items: [
            {
              productCode: ProductCodes.CompanyTrackerPro,
            },
          ],
        } as Subscription,
      ]);

      await expect(
        service.addCompanyTracker({
          initiative: initiativeMaterialityTracker as unknown as InitiativeModel,
          user: userOne,
        })
      ).to.eventually.be.rejectedWith(UserError, 'Your organization has already connected Company Tracker');
    });

    describe('integrate MT with CT successfully', () => {
      let createProductSubscriptionStub: SinonStub;
      let createCTSurveyStub: SinonStub;
      let getLatestMTMetricGroupStub: SinonStub;

      beforeEach(() => {
        sandbox.stub(customerManager, 'getSubscriptions').resolves([]);
        createProductSubscriptionStub = sandbox.stub(customerManager, 'createProductSubscription').resolves({} as any);
        createCTSurveyStub = sandbox.stub(selfOnboardingManager, 'createNewSurvey');
        getLatestMTMetricGroupStub = sandbox.stub(MTMetricGroupService, 'getLatestMetricGroup');
      });

      it('should create CT product subscription and update appConfigCode and permissionGroup of initiative', async () => {
        const initiative = { ...initiativeMaterialityTracker, save: sandbox.stub() };
        await service.addCompanyTracker({ initiative: initiative as unknown as InitiativeModel, user: userOne });

        expect(createProductSubscriptionStub.firstCall.firstArg.productCode).to.deep.equal(defaultCTPro.productCode);
        expect(initiative.permissionGroup).to.equal(defaultCTPro.permissionGroup);
        expect(initiative.appConfigCode).to.equal(defaultCTPro.code);
        expect(initiative.save.calledOnce).to.be.true;
      });

      it('should create survey for CT if no completed assessment is found', async () => {
        const initiative = { ...initiativeMaterialityTracker, save: sandbox.stub() };
        getLatestMTMetricGroupStub.resolves(undefined);

        await service.addCompanyTracker({ initiative: initiative as unknown as InitiativeModel, user: userOne });
        const [_initiative, _user, createSurveyData] = createCTSurveyStub.args[0];
        expect(createSurveyData.effectiveDate).to.deep.equal(getUTCEndOf('month', new Date()).toISOString());
        expect(createSurveyData.scope.custom).to.deep.equal([]);
        expect(createSurveyData.period).to.deep.equal(DataPeriods.Yearly);
      });

      it('should create survey for CT and add latest custom module', async () => {
        const initiative = { ...initiativeMaterialityTracker, save: sandbox.stub() };
        const metricGroupId = new ObjectId();
        const assessmentEffectiveDate = new Date('2024-12-31T23:59:59.999Z');
        getLatestMTMetricGroupStub.resolves({ metricGroupId, effectiveDate: assessmentEffectiveDate });

        await service.addCompanyTracker({ initiative: initiative as unknown as InitiativeModel, user: userOne });
        const [_initiative, _user, createSurveyData] = createCTSurveyStub.args[0];
        expect(createSurveyData.effectiveDate).to.deep.equal(assessmentEffectiveDate.toISOString());
        expect(createSurveyData.scope.custom).to.deep.equal([metricGroupId]);
        expect(createSurveyData.period).to.deep.equal(DataPeriods.Yearly);
      });
    });
  });

  describe('addMaterialityTracker', () => {
    it('should throw error if initiative connected MT before', async () => {
      sandbox.stub(customerManager, 'getSubscriptions').resolves([
        {
          items: [
            {
              productCode: ProductCodes.MaterialityTracker,
            },
          ],
          status: 'active',
        },
      ] as Subscription[]);

      await expect(
        service.addMaterialityTracker({
          initiative: initiativeCTPro as unknown as InitiativeModel,
          user: userOne,
        })
      ).to.eventually.be.rejectedWith(UserError, 'Your organization has already connected Materiality Tracker');
    });

    it('should create MT product subscription', async () => {
      sandbox.stub(customerManager, 'getSubscriptions').resolves([]);
      const createProductSubscriptionStub = sandbox.stub(customerManager, 'createProductSubscription');
      createProductSubscriptionStub.resolves({} as any);

      await service.addMaterialityTracker({
        initiative: initiativeCTPro as unknown as InitiativeModel,
        user: userOne,
      });

      expect(createProductSubscriptionStub.firstCall.firstArg).to.deep.equal({
        user: userOne,
        initiative: initiativeCTPro,
        productCode: defaultMT.productCode,
      });
    });
  });
});

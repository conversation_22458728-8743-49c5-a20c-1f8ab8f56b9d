/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import {
  SurveyActionDataAggregation,
  SurveyModelMinData,
  SurveyModelPlain,
  SurveyPermission,
  SurveyPermissionType,
  SurveyType,
} from '../../server/models/survey';
import { initiativeOneSimpleId } from './initiativeFixtures';
import {
  compositeSdg94Utrv,
  fragmentSdg9_4_3_id,
  fragmentSdg9_4_4_id,
  fragmentSdg9_4_5_id,
} from './compositeUTRVFixtures';
import { userOne } from './userFixtures';
import { DataPeriods, UtrvType } from '../../server/service/utr/constants';
import { DefaultBlueprintCode } from '../../server/survey/blueprints';
import { blueprintDefaultUnitConfig } from '../../server/service/units/unitTypes';
import { generatedUUID } from "../../server/service/crypto/token";
import { DataScopeAccess } from '../../server/models/dataShare';
import { SurveyUserRoles } from '../../server/types/roles';
import { Scope } from '../../server/models/common/scope';

export const createSurvey = (overrides: Partial<SurveyModelPlain> = {}): SurveyModelPlain => ({
  type: SurveyType.Default,
  period: DataPeriods.Yearly,
  references: [],
  sourceItems: [],
  visibleUtrvs: [],
  _id: new ObjectId(),
  name: 'test',
  code: 'test2',
  sourceName: DefaultBlueprintCode,
  initiativeId: initiativeOneSimpleId,
  created: new Date('2019-02-05T12:06:21.187Z'),
  effectiveDate: new Date('2019-02-05T14:06:21.187Z'),
  utrvType: UtrvType.Baseline,
  visibleStakeholders: [],
  evidenceRequired: false,
  verificationRequired: false,
  stakeholders: {
    stakeholder: [],
    verifier: [],
    escalation: [],
  },
  compositeUtrvs: [
    compositeSdg94Utrv._id,
  ],
  subFragmentUtrvs: [],
  disabledUtrvs: [],
  fragmentUtrvs: [
    fragmentSdg9_4_3_id,
    fragmentSdg9_4_4_id,
    fragmentSdg9_4_5_id,
  ],
  unitConfig: blueprintDefaultUnitConfig,
  ...overrides,
});

export const surveyOne: SurveyModelPlain = createSurvey();

export const surveyCreateData: SurveyModelMinData = {
  type: SurveyType.Default,
  period: DataPeriods.Yearly,
  sourceItems: [],
  visibleStakeholders: [],
  visibleUtrvs: [],
  name: 'test',
  code: 'test2',
  sourceName: DefaultBlueprintCode,
  initiativeId: initiativeOneSimpleId,
  effectiveDate: new Date('2019-03-05T14:06:21.187Z'),
  utrvType: UtrvType.Baseline,
  evidenceRequired: false,
  verificationRequired: false,
  stakeholders: {
    stakeholder: [userOne._id],
    verifier: [],
    escalation: [],
  },
  unitConfig: blueprintDefaultUnitConfig
};


export const createSurveyAction = (overrides: Partial<SurveyActionDataAggregation> = {}): SurveyActionDataAggregation => ({
  type: SurveyType.Default,
  period: DataPeriods.Yearly,
  _id: new ObjectId().toString(),
  name: `override-${generatedUUID()}`,
  code: 'override-' + generatedUUID(),
  sourceName: DefaultBlueprintCode,
  initiativeId: initiativeOneSimpleId,
  created: new Date('2019-02-05T12:06:21.187Z').toString(),
  effectiveDate: new Date('2019-02-05T14:06:21.187Z'),
  utrvType: UtrvType.Actual,
  evidenceRequired: false,
  verificationRequired: false,
  stakeholders: {
    stakeholder: [],
    verifier: [],
    escalation: [],
  },
  list: [],
  initiatives: [],
  fragmentUniversalTrackerValues: [],
  fragmentUniversalTracker: [],
  unitConfig: blueprintDefaultUnitConfig,
  ...overrides,
});


export const createSurveyPermission = (overrides: Partial<SurveyPermission> = {}): SurveyPermission => ({
  type: SurveyPermissionType.Workgroup,
  modelId: new ObjectId(),
  access: DataScopeAccess.Full,
  scope: {
    sdg: [],
    materiality: [],
    frameworks: [],
    standards: [],
    custom: [],
  } satisfies Scope,
  roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
  ...overrides,
});

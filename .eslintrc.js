/*
Happy linting! 💖
*/
module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'unused-imports', 'import'],
  parserOptions: {
    project: ['./tsconfig.json'],
    tsconfigRootDir: __dirname,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended-type-checked',
    'plugin:import/typescript',
    // Style related changes can be added at later date
    // 'plugin:@typescript-eslint/stylistic',
  ],
  settings: {
    'import/extensions': ['.js', '.ts'],
  },
  rules: {
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/explicit-module-boundary-types': 0,
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/no-empty-function': 0,
    '@typescript-eslint/no-inferrable-types': 0,
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-unsafe-return': 0,

    // Seems like there is no problem here at all. Very strict
    '@typescript-eslint/no-unsafe-enum-comparison': 0,
    '@typescript-eslint/require-await': 0,

    // Type related warnings and errors we don't want to deal with right now
    '@typescript-eslint/no-unsafe-member-access': 0,
    '@typescript-eslint/restrict-template-expressions': 0,
    '@typescript-eslint/consistent-type-definitions': 0,
    '@typescript-eslint/consistent-type-assertions': 0,
    '@typescript-eslint/consistent-indexed-object-style': 0,
    '@typescript-eslint/consistent-generic-constructor': 0,
    '@typescript-eslint/no-for-in-array': 0,

    // This seems to have bugs with declarations https://github.com/microsoft/TypeScript/issues/38347
    '@typescript-eslint/no-base-to-string': 0,

    // Should fix these as some point
    '@typescript-eslint/no-unsafe-assignment': 'warn',
    '@typescript-eslint/no-unsafe-argument': 'warn',
    '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
    '@typescript-eslint/no-unsafe-call': 'warn',
    '@typescript-eslint/no-redundant-type-constituents': 'warn',
    '@typescript-eslint/restrict-plus-operands': 'warn',
    '@typescript-eslint/await-thenable': 'warn',
    '@typescript-eslint/unbound-method': 'warn',

    '@typescript-eslint/no-floating-promises': ['warn', { ignoreVoid: true }],

    '@typescript-eslint/no-misused-promises': [
      'error',
      {
        checksConditionals: true,
        checksVoidReturn: false,
      },
    ],
    'prefer-const': 'warn',
    '@typescript-eslint/ban-types': 0,
    'no-useless-escape': 0,
    'unused-imports/no-unused-imports': 'error',
    'import/no-cycle': ['warn', { maxDepth: Infinity }],
    'import/no-restricted-paths': [
      'warn',
      {
        zones: [
          {
            target: './server/models/**/*',
            from: ['./server/service', './server/repository'],
            message: 'Models should not import service and repository.',
          },
        ],
      },
    ],

    'max-params': 'off',
    '@typescript-eslint/max-params': ['warn', { max: 5 }],

    'no-restricted-syntax': [
      'warn',
      {
        selector: "NewExpression[callee.name='Error']",
        message: 'Use ContextError() or UserError() instead of Error().',
      },
    ],
    'no-restricted-imports': [
      'warn',
      {
        patterns: [
          {
            group: ['**/SurveyUsers'],
            importNames: ['SurveyUserRoles'],
            message: "Import from 'server/types/roles' instead to avoid circular dependencies.",
          },
          {
            group: ['**/unitTypes'],
            importNames: ['SupportedMeasureUnits', 'NumberScale', 'Unit', 'validUnitTypes', 'UnitConfig'],
            message: "Import from 'server/types/units' instead to avoid circular dependencies.",
          },
          {
            group: ['**/utr/constants'],
            importNames: ['ActionList', 'ActionMap', 'UtrvType', 'DataPeriods'],
            message: "Import from 'server/types/constants' instead to avoid circular dependencies.",
          },
        ],
        paths: [
          {
            name: 'moment',
            message:
              'Direct usage of "moment" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
          },
          {
            name: 'dayjs',
            message:
              'Direct usage of "dayjs" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
          },
        ],
      },
    ],
    '@typescript-eslint/no-var-requires': 'warn',
    '@typescript-eslint/consistent-type-imports': ['warn', {
      prefer: 'type-imports', // enforce `import type { Foo } from ...`
      disallowTypeAnnotations: true,
    }],
  },
  overrides: [
    {
      files: ['*.js'],
      extends: ['plugin:@typescript-eslint/disable-type-checked'],
    },
    // To many warnings there to deal with, will need to gradually fix those
    {
      files: ['*.spec.ts', 'tests/**/*'],
      extends: ['plugin:@typescript-eslint/disable-type-checked'],
    },
  ],
  env: {
    es6: true,
    node: true,
  },
  ignorePatterns: ['node_modules/', 'dist/', 'coverage/', 'migrations/'],
};

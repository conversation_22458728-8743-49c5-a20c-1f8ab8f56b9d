/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import { SubmissionInsightsView } from "../../repository/PortfolioRepository";
import Initiative, {
  InitiativeModel,
  InitiativePlain,
  InitiativeTypes,
  PERMISSION_GROUPS
} from "../../models/initiative";
import InitiativeGroup from "../../models/initiativeGroup";
import { getIndustryLevelsText, getIndustryText, getSectorText, getSectorTextByLevel } from "../reporting/FrameworkMapping";
import { DataScope, DataScopeAccess } from "../../models/dataShare";
import { ActiveDataShare, DataShareMin } from "../../repository/DataShareRepository";
import { PortfolioLatestSurvey } from '../../repository/SurveyRepository';
import {
  createDataScopeAccess,
  generateKeyFromResult,
  ScopeValidationResult,
  ValidateParams,
  validateScope
} from '../share/dataShareUtil';
import UserError from "../../error/UserError";
import MetricGroup, { MetricGroupModel } from "../../models/metricGroup";
import { KeysEnum } from "../../models/public/projectionUtils";
import { FilterQuery, isValidObjectId } from "mongoose";
import { BankingCodeService } from "../banking/BankingCodeService";
import { RequestScope } from "../survey/model/DelegationScope";
import { DateRange } from "../../util/date";

export type PortfolioMetricGroup = Pick<MetricGroupModel, '_id' | 'groupName' | 'universalTrackers'>;
const ptMetricGroupProjection: KeysEnum<PortfolioMetricGroup> = {
  _id: 1,
  groupName: 1,
  universalTrackers: 1
}


export interface ExchangeStats {
  private: number;
  na: number;
  nr: number;
  answered: number;
  total: number;
}
export interface ExchangeSurvey extends ExchangeStats {
  _id: string;
  name: string;
  completedDate: string;
  effectiveDate: string;
  period: string;
  [key: string]: any;
}

export type ExtendedInitiativePlain = Pick<
  InitiativePlain,
  | '_id'
  | 'code'
  | 'name'
  | 'type'
  | 'initiativeGroupId'
  | 'industry'
  | 'sectorText'
  | 'industryText'
  | 'permissionGroup'
  | 'created'
  | 'appConfigCode'
> & {
  /** It will contain Accepted and *Pending* statuses **/
  requestedDataShares?: DataShareMin[]
};

const extendedInitiativeProjection: KeysEnum<ExtendedInitiativePlain> = {
  _id: 1,
  code: 1,
  name: 1,
  type: 1,
  initiativeGroupId: 1,
  industry: 1,
  permissionGroup: 1,
  sectorText: 1,
  industryText: 1,
  requestedDataShares: 1,
  created: 1,
  appConfigCode: 1
};

export type PortfolioCompany = ExtendedInitiativePlain & {
  tags?: string[];
  dataShare?: { dataScope?: DataScope };
  exchangeSurveys?: ExchangeSurvey[];
  latestSurvey?: PortfolioLatestSurvey;
};

export const portfolioCompanyProjection: KeysEnum<Omit<PortfolioCompany, 'exchangeSurveys' | 'latestSurvey'>> = {
  ...extendedInitiativeProjection,
  tags: 1,
  dataShare: 1
}

export type InitiativeBenchmarking = ExtendedInitiativePlain & {
  benchmarkingSurveys: ExchangeSurvey[];
  latestSurvey: ExchangeSurvey | undefined;
};

export type PackUsageScopes = {
  group: string | undefined;
  subGroup: string | undefined;
  leafGroup: string | undefined;
  leafChildGroup: string | undefined;
}

export interface InitiativeFilterProps {
  sector: string | undefined;
  industry: string | undefined;
}

export type PackUsageQuery = PackUsageScopes & DateRange & InitiativeFilterProps & { scopeGroups: RequestScope[] };

export interface AddHoldingToGroup {
  portfolio: InitiativePlain;
  holdingId: string;
  weight: number;
}

interface InitiativeFilters extends InitiativeFilterProps {
  initiative: ExtendedInitiativePlain;
}

interface CompanyGroupingResult {
  key: string,
  initiativeIds: ObjectId[],
  validatedScope: ScopeValidationResult
}

export class PortfolioService {
  public static async removeCompanyFromAllPortfolios(initiative: Pick<InitiativePlain, '_id'>) {
    const portfolioTrackers = await InitiativeGroup.find({ 'group.initiativeId': initiative._id }).exec();
    if (portfolioTrackers.length === 0) {
      return;
    }

    return Promise.all(
      portfolioTrackers.map((portfolioTracker) => {
        portfolioTracker.group = portfolioTracker.group.filter((group) => !group.initiativeId.equals(initiative._id));
        return portfolioTracker.save();
      })
    );
  }

  public static async findInitiativeWithIndustry(id: string) {
    const initiative: ExtendedInitiativePlain | null = await Initiative.findById(id, extendedInitiativeProjection).lean();
    if (!initiative) {
      return;
    }
    initiative.sectorText = getSectorText(initiative.industry);
    initiative.industryText = getIndustryText(initiative.industry);
    return initiative;
  }

  public static async getRecursiveInitiatives(
    portfolio: InitiativePlain<ObjectId>,
    maxRecursion: number,
    filters?: InitiativeFilterProps
  ) {

    const companies: Map<string, ExtendedInitiativePlain> = new Map();
    const recursiveInitiativeGroup = async (id: string, depth: number = 1) => {
      const initiative = await this.findInitiativeWithIndustry(id);
      if (!initiative) {
        return;
      }
      if (
        initiative.type === InitiativeTypes.Initiative &&
        portfolio.permissionGroup === PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE
      ) {
        const iid = String(initiative._id);
        if (companies.has(iid)) {
          return;
        }
        if (filters && this.filterBySectorAndIndustry({ initiative, ...filters })) {
          return;
        }
        return companies.set(iid, {
          ...initiative,
        });
      }
      if (depth > maxRecursion) {
        return;
      }
      const group = await InitiativeGroup.findById(initiative.initiativeGroupId).lean();
      if (group) {
        for (const g of group.group) {
          await recursiveInitiativeGroup(String(g.initiativeId), depth + 1);
        }
      }
    };
    await recursiveInitiativeGroup(String(portfolio._id));
    return companies;
  }

  private static filterBySectorAndIndustry({ initiative, sector, industry }: InitiativeFilters) {
    if (!sector) {
      return false;
    }
    if (industry) {
      return industry !== initiative.industryText || sector !== initiative.sectorText;
    }
    return sector !== initiative.sectorText;
  }

  public static getDisplayNameByView(view: string, id: string, companies: Map<string, ExtendedInitiativePlain>) {
    if (view === SubmissionInsightsView.Company) {
      return companies.get(String(id))?.name ?? id;
    }
    return getSectorTextByLevel(id);
  }

  public static getIndustryClassificationsText(view: string, id: string, companies: Map<string, ExtendedInitiativePlain>) {
    if (view !== SubmissionInsightsView.Company) {
      return {}
    }
    const company = companies.get(String(id));
    if (!company || !company.industry) {
      return {}
    }
    return getIndustryLevelsText(company.industry)
  }

  /**
   *  Either standard/frameworks (not valid) or custom metric group id is passed (valid)
   *  Load metric for the PT as well as referralCode shared metric groups
   *
   *  Use metricGroupIds to limit the lookup from valid metric groups available
   */
  public static async getPortfolioMetricGroups(
    portfolio: Pick<InitiativePlain, '_id' | 'code'>,
    metricGroupIds: (string | ObjectId)[]
  ): Promise<PortfolioMetricGroup[]> {
    const customScopes = metricGroupIds.filter(s => isValidObjectId(s));

    const customMetricIds = await BankingCodeService.getPortfolioMappingMetricGroupIds(portfolio.code)
    if (customScopes.length > 0) {
      // Only requested scope should match for banking ones
      const requestedIds = customScopes.map(String)
      const filteredGroupIds = customMetricIds.filter(id => requestedIds.includes(id.toString()));

      const $or: FilterQuery<MetricGroupModel>[] = [
        {
          _id: { $in: customScopes.map(id => new ObjectId(id)) },
          initiativeId: portfolio._id
        },
      ];
      if (filteredGroupIds.length > 0) {
        $or.push({ _id: { $in: filteredGroupIds } })
      }

      return MetricGroup.find({ $or }, ptMetricGroupProjection).exec();
    }

    // Otherwise all required metrics
    return MetricGroup.find({
      $or: [
        { initiativeId: portfolio._id },
        { _id: { $in: customMetricIds } }
      ]
    }, ptMetricGroupProjection).exec();
  }

  public static async getSharedScopesCompanies({
    requestedScope,
    activeDataShares,
  }: {
    requestedScope: ValidateParams['downloadScope'];
    activeDataShares: ActiveDataShare[];
  }) {
    const dataSharesMap = activeDataShares.reduce((acc, dataShare) => {
      const initiativeId = dataShare.initiativeId.toString();
      const entry = acc.get(initiativeId);
      entry ? entry.push(dataShare) : acc.set(initiativeId, [dataShare]);
      return acc;
    }, new Map<string, ActiveDataShare[]>());

    const companyGrouping: Record<string, CompanyGroupingResult> = {}
    Array.from(dataSharesMap).forEach(([id, dataShares]) => {

      const dataScope = createDataScopeAccess({ dataShares });
      const validatedScope = validateScope({ downloadScope: requestedScope, dataScope });
      if (validatedScope.access === DataScopeAccess.None) {
        return;
      }
      const key = generateKeyFromResult(validatedScope)

      if (!companyGrouping[key]) {
        companyGrouping[key] = { key, validatedScope, initiativeIds: [] }
      }
      companyGrouping[key].initiativeIds.push(new ObjectId(id));
    })
    return companyGrouping
  }

  // Now check if the resulting mashup has circular dependencies, in which case don't save and error out
  // I think I only need to check children as recursive issues will appear as a child? Hopefully...
  public static async checkFilteredIdsRecursive(initiative: InitiativeModel) {
    const filteredIds: Set<string> = new Set();
    const checkFilteredIds = async (initiativeId: string) => {
      const allGroupsWithPortfolio = await InitiativeGroup.find({ 'group.initiativeId': initiativeId }, { _id: 1 }).exec();
      const filteredInitiatives = await Initiative.find({
        initiativeGroupId: { $in: allGroupsWithPortfolio.map((g) => g._id) },
      }).exec();
      await Promise.all(
        filteredInitiatives.map(async (f) => {
          const id = String(f._id);
          if (filteredIds.has(id)) {
            throw new UserError(`Error - You cannot add this holding as it is a parent to this portfolio.`);
          }

          filteredIds.add(id);
          return checkFilteredIds(id);
        })
      );
    };
    await checkFilteredIds(String(initiative._id));
  }

  public static async getExistingHolding({ portfolio, holdingId }: Pick<AddHoldingToGroup, 'portfolio' | 'holdingId'>) {
    const initiativeGroup = await InitiativeGroup.findById(portfolio.initiativeGroupId).orFail().exec();
    const existingHolding = initiativeGroup.group.find((i) => String(i.initiativeId) === holdingId);

    return {
      initiativeGroup,
      existingHolding
    }
  }

  public static async addHoldingToGroup({ portfolio, holdingId, weight }: AddHoldingToGroup) {
    const { initiativeGroup, existingHolding } = await this.getExistingHolding({ portfolio, holdingId });

    if (existingHolding) {
      throw new UserError('Holding already in portfolio');
    }

    // Check if valid holding
    const initiative = await Initiative.findOne({
      _id: new ObjectId(holdingId),
      type: { $ne: InitiativeTypes.Group },
      // Not sure why this was restricted to non-PT initiatives, but this was
      // causing HTTP 500 in test companies that may no longer by InitiativeGroup, but
      // still have initiativeGroupId
      // initiativeGroupId: { $exists: false },
    })
      .orFail()
      .exec();

    if (!initiative) {
      throw new UserError('Invalid holding');
    }

    if (initiative.initiativeGroupId) {
      await this.checkFilteredIdsRecursive(initiative);
    }

    initiativeGroup.group.push({
      weight,
      initiativeId: initiative._id,
    });

    return initiativeGroup.save();
  }

  public static async removeHoldingFromGroup(portfolio: Pick<InitiativePlain, 'initiativeGroupId'>, removeInitiativeId: string) {
    const portfolioGroup = await InitiativeGroup.findById(portfolio.initiativeGroupId).orFail().exec();
    portfolioGroup.group = portfolioGroup.group.filter((i) => String(i.initiativeId) !== removeInitiativeId);
    await portfolioGroup.save();

    return portfolioGroup;
  }
}

/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BaseTemplateOptions, getBaseTemplate } from './template/base';
import { Fact, Header, SectionOption, SectionResult, XbrlTable } from './csrdTypes';

export const renderCSRD = (options: BaseTemplateOptions) => getBaseTemplate(options);

const createAnchorHeading = ({ title, id }: Header) => {
  return `<div class="defaultParagraph Heading1"><span id="${id}" class="hyperlink-no-style"/>${title}</div>`;
};

const Spacer = `<div class="defaultParagraph Normal" style="min-height:15px"/>`;
const BreakLineSpacer = `<div class="defaultParagraph Normal"><br/></div>`;

export function nl2br(str: string | number | undefined | null): string {
  if (typeof str === 'undefined' || str === null || str === '') {
    return '';
  }

  const regex = /([^>\r\n]?)(\r\n|\n\r|\r|\n)/g;
  const breakTag = '<br />';
  return (str + '')
    .split(regex)
    .map((txt) => (regex.test(txt) ? breakTag + txt : txt))
    .join('');
}

function renderTable(child: XbrlTable) {
  const { columns, data } = child;

  const getHeaderClass = (index: number) => {
    if (index === 0) {
      return 'd23a3a1';
    }
    if (index === columns.length - 1) {
      return 'ec0518';
    }
    return 'd026dd4';
  };

  const getTableClass = (index: number) => {
    if (index === 0) {
      return 'd43cab8';
    }
    if (index === columns.length - 1) {
      return 'f9ae73';
    }
    return 'ac0919';
  };

  const headerRow = columns
    .map(({ name }, index) => {
      return `<td class="${getHeaderClass(index)}"><div class="defaultParagraph Normal">${name}</div></td>`;
    })
    .join('');

  const dataRows = data
    .map((row) => {
      const cols = columns.map(({ accessor }) => {
        const data = typeof accessor === 'function' ? accessor(row) : row[accessor];
        if (typeof data === 'object' && 'type' in data && data.type === 'fact') {
          return generateFactContent(data);
        }
        return data ?? '';
      });

      const tableColumns = cols.map((col, index) => {
        return `<td class="${getTableClass(index)} defaultParagraph">${col}</td>`;
      });

      return `<tr>${tableColumns.join('')}</tr>`;
    })
    .join('');

  return `<table class="TableGrid" style="width: 601px; border-collapse: collapse; margin: 0px auto 0px 0px; border-style: none; position: relative;" dir="ltr"> ${headerRow}${dataRows}</table> `;
}

const renderChildren = (child: SectionOption['children'][0]): string => {
  switch (child.type) {
    case 'inline':
      return nl2br(String(child.content));
    case 'table':
      return renderTable(child);
    case 'fact': {
      return generateFactContent(child);
    }
    case 'html': {
      return child.content;
    }
    default:
      return '';
  }
};

export const generateSection = (options: SectionOption): SectionResult => {
  const { header, children, footer } = options;

  // Need to figure out how many pages we need to create based on size
  const content = children.map((fact) => renderChildren(fact)).join('');

  // Add more pages based on content size, need to come up with formula
  if (content.length > 4000) {
    // should split
  }

  const firstPage = `
  <div class="page_after_0">
    <div class="container">
      <div class="page_header_after_0">
        <div class="defaultParagraph Header" style="min-height:15px"/>
      </div>
      <div class="page_body_after_0">
        ${createAnchorHeading(header)}
        ${Spacer}
        ${content}
        ${BreakLineSpacer}
      </div>
      ${footer ?? ''}
    </div>
  </div>
  `;

  return { header, pages: [firstPage] };
};

export const getDefaultFooter = () => {
  return `<div class="page_footer_after_0">
      <div class="defaultParagraph Footer">
        <hr/>
        <p>
        <small>
          This document has been prepared by the G17Eco platform.
          © G17Eco ${new Date().getUTCFullYear()} - <a target="_blank" href="https://g17.eco">https://g17.eco</a>
          </small>
        </p></div>
    </div>`;
};

const generateDefaultParagraph = (text: string | number): string => {
  if (text === '') {
    return '';
  }

  return `<div class="defaultParagraph Normal">
      <span style="font-size:14px; min-height:14px; font-family:'Open Sans'; color:#000000" dir="ltr">${text}</span>
    </div>`;
};

const generateText = (text: string | number, tag?: 'p' | 'span'): string => {
  switch (tag) {
    case 'p':
      return generateDefaultParagraph(text);
    default:
      return `<span style="font-size:14px; min-height:14px; font-family:'Open Sans'; color:#000000" dir="ltr">${text}</span>`;
  }
};

export const generateFactContent = (fact: Fact) => {
  const innerContent = fact.children.map((child) => renderChildren(child));

  if (fact.tag === 'ix:nonFraction') {
    const { id, tag, name, decimals = '', unitRef = '', scale = '', contextRef } = fact;

    return `<${tag} name="${name}" id="${id}" unitRef="${unitRef}" contextRef="${contextRef}" scale="${scale}" decimals="${decimals}">
   ${innerContent.join('')}
   </${tag}>`;
  }

  const { id, tag = 'ix:nonNumeric', name, lang = 'en', escape = 'true', format, contextRef } = fact;

  const fmt = format ? `format="${format}"` : '';

  return `<${tag} name="${name}" id="${id}" ${fmt} contextRef="${contextRef}" escape="${escape}" xml:lang="${lang}">
   ${innerContent.join('')}
   </${tag}>`;
};

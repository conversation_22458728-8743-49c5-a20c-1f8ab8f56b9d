/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { SurveyModelPlain } from '../../../models/survey';
import { InitiativePlain } from '../../../models/initiative';
import { UtrvData, XBRLMapping } from '../esrs/types';
import { XbrlTracker } from '../XbrlTracker';

export interface Header {
  title: string;
  id: string;
}

export interface SectionOption {
  header: Header;
  children: ChildrenNode[];
  footer?: string;
}

export interface SectionResult {
  header: SectionOption['header'];
  pages: string[];
}

type NonNumericFormat = 'ixt4:fixed-true';

export type FactId = `fact-${number}`;
export type ContextId = `c-${number}`;
export type UnitId = `u-${number}`;

export interface NonNumericFact {
  tag?: 'ix:nonNumeric';
  name: string;
  id: FactId;
  contextRef: string;
  escape?: 'true' | 'false' | boolean;
  format?: NonNumericFormat;
  lang?: string;
  children: ChildrenNode[];
}

export interface NonFractionFact {
  tag: 'ix:nonFraction';
  name: string;
  format?: string;
  id: FactId;
  contextRef: ContextId;
  children: ChildrenNode[];
  unitRef?: UnitId;
  decimals?: number;
  scale?: number | string;
}

export type Fact = NonNumericFact | NonFractionFact;

type TextTag = 'span' | 'p';

export type FactTag = { type: 'fact' } & Fact;
type PlainText = { type: 'text'; tag?: TextTag; content: string | number };
type InlineText = { type: 'inline'; content: string | number };
type Group = { type: 'group'; tag?: TextTag; children: ChildrenNode[] };
type HTMLNode = { type: 'html'; content: string };

interface ColumnSetup<T = any> {
  id: string;
  name: string;
  accessor: string | ((utr: T) => string | number | undefined | FactTag);
}

export type XbrlTableData = Record<string, FactTag | string | undefined>;
export type XbrlTable<T = XbrlTableData> = {
  type: 'table';
  columns: ColumnSetup<T>[];
  data: T[];
};

export type ChildrenNode = PlainText | InlineText | FactTag | XbrlTable | Group | HTMLNode;

export interface ReportSection {
  header: Pick<SectionOption['header'], 'title'>;
  children: ChildrenNode[];
}

export interface SectionData {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  mapping: XBRLMapping;
  utrCodeToUtrvMap: Map<string, UtrvData>;
  tracker: XbrlTracker;
}

export interface GeneratorParameters {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  survey: SurveyModelPlain;
  mapping: XBRLMapping;
  utrvData: UtrvData[];
  utrCodeToUtrvMap: Map<string, UtrvData>;
}

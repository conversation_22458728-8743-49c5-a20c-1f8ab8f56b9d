/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { generateSection, getDefaultFooter, renderCSRD } from './csrdBaseTemplate';
import { getXbrlHeader } from './xbrlHeader';
import { getTableOfContents, getTitlePage } from './titlePage';
import { InitiativePlain } from '../../../models/initiative';
import { ReportSection, SectionOption } from './csrdTypes';

interface XhtmlData {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  effectiveDate: Date;
  sections: ReportSection[];
  footer?: string;
  /** Custom scripts and js scripts to inject **/
  customScripts?: string;
}

export class XhtmlGenerator {
  // Assume we can fit 10 items per page
  private numberOfItemsInTocPage = 10;

  /**
   * Take a nested list of facts and convert into rendered XHTML content
   */
  public async render(data: XhtmlData) {
    const { initiative, effectiveDate, sections, footer = getDefaultFooter(), customScripts } = data;

    let anchorCounter = 1;
    const getAnchorId = () => `ToC-anchor-${anchorCounter++}`;

    const renderedSections = sections.map((section) => {
      return generateSection({
        header: {
          title: section.header.title,
          id: getAnchorId(),
        },
        children: section.children,
        footer,
      });
    });

    let content = '';
    const totalSections: { header: SectionOption['header']; pageCount: number }[] = [];
    renderedSections.forEach(({ header, pages }) => {
      content += pages.join('');
      totalSections.push({ header, pageCount: pages.length });
    });

    const tocPageSize = totalSections.reduce((acc, { pageCount }) => {
      return acc + pageCount;
    }, 0);

    // Starting page after title and Table of Contents. Assume title.
    let pageNumber = this.getTocPages(tocPageSize);

    const tableOfContents = getTableOfContents({
      title: 'Table of Contents',
      items: totalSections.map(({ header, pageCount }) => {
        // Increment and assign page number
        pageNumber += pageCount;
        return {
          title: header.title,
          href: `#${header.id}`,
          page: pageNumber,
        };
      }),
    });

    const year = effectiveDate.getUTCFullYear();

    return renderCSRD({
      title: `ESRS Report ${initiative.name} ${year}`,
      titlePage: getTitlePage({
        title: `${initiative.name} ${year}`,
        subTitle: 'Management and Sustainability Report',
        footer,
        tableOfContents,
      }),
      xbrl: {
        header: getXbrlHeader(),
      },
      content,
      customScripts,
    });
  }

  private getTocPages(numberOfPages: number) {
    return Math.ceil(numberOfPages / this.numberOfItemsInTocPage);
  }
}

let instance: XhtmlGenerator;
export const getXhtmlGenerator = () => {
  if (!instance) {
    instance = new XhtmlGenerator();
  }
  return instance;
};

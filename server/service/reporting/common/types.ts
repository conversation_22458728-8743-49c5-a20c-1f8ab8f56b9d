

import type { FactId } from '../xhtml/csrdTypes';

type NonUnitKey = Exclude<string, 'u'>;

export type FactsMap<T extends NonUnitKey = NonUnitKey> = Record<FactId, DebugFact<T> | undefined>;

export interface DebugFact<T extends NonUnitKey = NonUnitKey> {
  /**
   * Value of the fact
   *
   *
   * Inline Html with encoded tags
   * "\u003Cdiv class=\u0022defaultParagraph Normal\u0022
   * xmlns=\u0022http://www.w3.org/1999/xhtml\u0022\u003E\u003Cspan
   * id=\u0022ID_528844629\u0022\u003ENulla ac augue at mauris gravida ultrices.
   * \u003Cspan id=\u0022ID_1440257468\u0022\u003EPell ..."
   *
   * ----- OR -----
   * "v": "true"
   */
  v: string;
  a: FactAnnotation<T>;
  /**
   * Assume this is Format
   * @example  "f": "ixt:fixed-true"
   */
  f?: string;

  /** Decimal? **/
  d?: number;
}

/**
 * @example
 *  "a": {
 *    "c": "esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory",
 *    "e": "e:efrag",
 *    "esrs:IdentifierOfImpactRiskAndOpportunityTypedAxis": "1",
 *    "p": "2024-01-01/2025-01-01"
 *  }
 */

type FactAnnotation<T extends NonUnitKey = NonUnitKey> = Record<T, string> & {
  /** Reference to concept? Concept Name */
  c: string;
  /**
   * Always seems to be organization entity e:efrag, but based on viewer code it's identifier
   *     identifier() {
   *         return this.report.qname(this.f.a.e);
   *     }
   *     based on type it can be one of the schemas
   *
   *  const schemes = {
   *   "http://standards.iso.org/iso/17442": { "name": "LEI", "url": "https://search.gleif.org/#/record/%s" },
   *   "http://www.sec.gov/CIK": { "name": "CIK", "url": "https://www.sec.gov/cgi-bin/browse-edgar?CIK=%s"},
   *   "http://www.companieshouse.gov.uk/": { "name": "UK CRN", "url": "https://beta.companieshouse.gov.uk/company/%08d"},
   *   "https://www.minfin.gov.ua": { "name": "EDRPOU" },
   *  };
   */
  e: string;
  /** Period - Date range? */
  p: string;
  /** Unit?  "u": "iso4217:EUR", */
  u?: string;
}
import { UniversalTrackerPlain } from '../../../models/universalTracker';
import { UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';

export type EsrsFact = `esrs:${string}`;

export interface ESRSMappingItem {
  factName: EsrsFact;
  utrCode: string;
  valueListCode?: string;
}

export interface XBRLMapping {
  [key: string]: ESRSMappingItem | undefined;
}

type BaseUtrv = Pick<UniversalTrackerValuePlain, '_id' | 'effectiveDate' | 'value' | 'valueData' | 'status'>;
export interface UtrvData extends BaseUtrv {
  universalTracker: UniversalTrackerPlain;
}

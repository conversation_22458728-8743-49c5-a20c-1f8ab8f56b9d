/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { FactsMap } from '../common/types';

export interface InlineViewerData {
  concepts: Record<string, Concept | undefined>;
  languages: Languages;
  facts: FactsMap<`esrs:${string}`>;
  prefixes: Prefixes;
  roles: Roles;
  roleDefs: RoleDefs;
  rels: Rels;
  validation: unknown[];
}

export interface Rels {
  pres: Pres;
}

export interface Pres {
  [k: `ns${number}`]: EsrsNameSpace;
}

export interface EsrsNameSpace {
  [k: `esrs:${string}`]: TextValue[];
}

export interface TextValue {
  t: string;
}

export interface RoleDefs {
  [k: `ns${number}`]: Languages;
}

export interface Roles {
  std: string;
  doc: string;
  calc: string;
  pres: string;
  'w-n': string;
  ns0: string;
  [k: string]: string;
}

export interface Prefixes {
  esrs_entry: string;
  esrs: string;
  e: string;
  iso4217: string;
  xbrli: string;
  utr: string;
}

export interface Languages {
  en: string;
}

export interface Labels {
  std: Languages;
  doc?: Languages;
  [k: `ns${number}`]: Languages | undefined;
}

type ReferenceItem = [label: string, value: string];
/**
 * @example
 *  [
 *    ["Name", "ESRS"],
 *    ["Number", "ESRS 2"],
 *    ["Paragraph", "48"],
 *    ["Subparagraph", "a"],
 *    ["Section", "SBM-3"]
 *  ]
 */
type ReferenceRow = ReferenceItem[];

/**
 * @example
 * {
 *   "esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory": {
 *     "labels": {
 *       "std": {
 *         "en": "Description of material impacts resulting from materiality assessment [text block]"
 *       }
 *     },
 *     "r": [
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "ESRS 2"],
 *         ["Paragraph", "48"],
 *         ["Subparagraph", "a"],
 *         ["Section", "SBM-3"]
 *       ],
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "E4"],
 *         ["Paragraph", "16"],
 *         ["Section", "SBM-3"]
 *       ]
 *     ],
 *     "t": true
 *   }
 * }
 */
interface Concept {
  labels: Labels;
  /** d: "t" not sure what this means for now  */
  d?: 't' | 'e' | number;
  r?: ReferenceRow[];
  t?: boolean;
  en?: boolean;
}

export interface ESRSDefinitionRow {
  Level: number;
  Role: string;
  'Label en': string;
  'Additional Label': string;
  'Technical Name': string;
  Abstract: string;
  'Type name short': string;
  'Period type': string;
  Balance: string;
  'Substitution Group': string;
  References: string;
}

export interface DefinitionMapItem {
  label: string;
  technicalName: string;
  references?: ReferenceRow[];
}

/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import {
  NotificationCategoryPreference,
  NotificationPreferencesModel,
  NotificationPreferencesPlain,
  NotificationServices,
} from './NotificationModels';
import { DomainConfig } from '../organization/domainConfig';
import { AppCode, AppConfig } from '../app/AppConfig';

export enum NotificationCategory {
  QuestionReject = 'question_reject',
  QuestionRejectPermission = 'question_reject_permission',
  QuestionDelegation = 'question_delegation',
  QuestionNoteUpdate = 'question_note_update',
  QuestionComment = 'question_comment',
  QuestionUpdate = 'question_update',
  QuestionUpdatePermission = 'question_update_permission',

  QuestionAssured = 'question_assured',
  QuestionDisputed = 'question_disputed',

  AssuranceStart = 'assurance_start',
  AssuranceQuestionUpdate = 'assurance_question_update',
  AssuranceComplete = 'assurance_complete',

  DataShareRequest = 'data_share_request',
  DataShareRevoke = 'data_share_revoke',
  DataShareReject = 'data_share_reject',
  DataShareAccept = 'data_share_accept',

  UserToUsers = 'user_to_users',
  UserToSurveyUsers = 'user_to_survey_users',
  UserToQuestionUsers = 'user_to_question_users',

  BulkSurveys = 'bulk_surveys',
  Announcements = 'announcements',
  ScheduleEmailSummaries = 'schedule_email_summaries'
}

export enum NotificationCategoryGroup {
  SurveysAndQuestions = 'surveys_and_questions',
  DataSharing = 'data_sharing',
  Assurance = 'assurance',
  User = 'user',
}

export const notificationCategoryGroupNameMap = {
  [NotificationCategoryGroup.SurveysAndQuestions]: 'Reporting',
  [NotificationCategoryGroup.DataSharing]: 'Data sharing',
  [NotificationCategoryGroup.Assurance]: 'Assurance',
  [NotificationCategoryGroup.User]: 'User',
}

export const NotificationCategoryGroupsMap = new Map<NotificationCategoryGroup, NotificationCategory[]>([
  [
    NotificationCategoryGroup.SurveysAndQuestions,
    [
      NotificationCategory.QuestionComment,
      NotificationCategory.QuestionDelegation,
      NotificationCategory.QuestionNoteUpdate,
      NotificationCategory.QuestionReject,
      NotificationCategory.QuestionRejectPermission,
      NotificationCategory.QuestionUpdate,
      NotificationCategory.QuestionUpdatePermission,
      NotificationCategory.BulkSurveys,
      NotificationCategory.Announcements,
    ],
  ],
  [
    NotificationCategoryGroup.DataSharing,
    [
      NotificationCategory.DataShareAccept,
      NotificationCategory.DataShareReject,
      NotificationCategory.DataShareRequest,
      NotificationCategory.DataShareRevoke,
    ],
  ],
  [
    NotificationCategoryGroup.Assurance,
    [
      NotificationCategory.AssuranceComplete,
      NotificationCategory.AssuranceQuestionUpdate,
      NotificationCategory.AssuranceStart,
      NotificationCategory.QuestionAssured,
      NotificationCategory.QuestionDisputed,
    ],
  ],
  [
    NotificationCategoryGroup.User,
    [
      NotificationCategory.UserToUsers,
      NotificationCategory.UserToSurveyUsers,
      NotificationCategory.UserToQuestionUsers,
    ],
  ],
]);

export enum NotificationRole {
  Assurer ='assurer',
  Admin = 'admin',
  User = 'user',
  PortfolioAdmin = 'portfolio_admin',
}

export const NotificationRoleMap = new Map<NotificationRole, NotificationCategory[]>([
  [
    NotificationRole.Assurer,
    [
      NotificationCategory.AssuranceStart,
      NotificationCategory.AssuranceQuestionUpdate
    ]
  ],
  [
    NotificationRole.Admin,
    [
      NotificationCategory.QuestionReject,
      NotificationCategory.QuestionRejectPermission,
      NotificationCategory.QuestionDelegation,
      NotificationCategory.QuestionNoteUpdate,
      NotificationCategory.QuestionComment,
      NotificationCategory.QuestionUpdate,
      NotificationCategory.QuestionUpdatePermission,
      NotificationCategory.QuestionAssured,
      NotificationCategory.QuestionDisputed,
      NotificationCategory.AssuranceComplete,
      NotificationCategory.DataShareRequest,
      NotificationCategory.BulkSurveys,
    ]
  ],
  [
    NotificationRole.User,
    [
      NotificationCategory.QuestionReject,
      NotificationCategory.QuestionRejectPermission,
      NotificationCategory.QuestionDelegation,
      NotificationCategory.QuestionNoteUpdate,
      NotificationCategory.QuestionComment,
      NotificationCategory.QuestionUpdate,
      NotificationCategory.QuestionUpdatePermission,
      NotificationCategory.QuestionAssured,
      NotificationCategory.QuestionDisputed,
    ]
  ],
  [
    NotificationRole.PortfolioAdmin,
    [
      NotificationCategory.DataShareAccept,
      NotificationCategory.DataShareRevoke,
      NotificationCategory.DataShareReject,
    ]
  ]
]);

export enum NotificationPage {
  QuestionView = 'question_view',
  SurveyOverview = 'survey_overview',
  SurveyAssurance = 'survey_assurance',
  ReportView = 'report_view',
  AssurerDashboard = 'assurer_dashboard',
  AssurerPortfolio = 'assurer_portfolio',
  AssurerPortfolioQuestion = 'assurer_portfolio_question',
  DataShareView = 'data_share_view',
  PortfolioDataShareView = 'portfolio_data_share_view',
  ManageUsers = 'manage_users',
  SurveyTemplates = 'survey_templates',
  TemplateHistory = 'template_history',
  SurveyOverviewRedirect = 'company_tracker_survey_redirector',
  CompanySettings = 'company_tracker_settings',
  CompanySettingsSurveyConfig = 'company_tracker_settings_survey_config',
  CompanySettingsAccountManagement = 'company_tracker_settings_account_management',
  CustomMetrics = 'company_tracker_custom_metrics',
  NavigateByMap = 'company_tracker_navigate_by_map',
  BulkSurveyImport = 'bulk_survey_import',
  ReportDownload = 'report_download',
  MaterialitySurveyOverview = 'assessment',
  MaterialityTrackerInsights = 'materiality_tracker_insights',

  IntegrationApp = 'integration_app',
}

interface SettingData {
  enabled?: boolean;
}

type NotificationSettings = Record<string, SettingData>;

export interface RecipientData {
  id: string;
  email?: string;
  firstName?: string;
  surname?: string;
  customAttributes?: Record<string, unknown>;
  settings?: NotificationPreferencesPlain;
  defaultSettings?: NotificationSettings;
}

export interface ExpandedRecipientData extends RecipientData {
  _id: ObjectId;
  email: string;
  settings: Readonly<NotificationPreferencesPlain>;
}

export interface Recipient {
  userId: ObjectId
}

interface CommonNotification {
  title: string;
  content: string;
  category: NotificationCategory
  topic?: string;
  actionUrl?: string;
  customAttributes?: CustomAttributes;
  created?: Date;
}

interface OverrideData {
  title: string,
  content: string,
  subject: string,
  actionText: string,
  bottomContent: string,
}

export type NotificationDataExpanded = CreateNotificationData<ExpandedRecipientData> & {
  domain: string | undefined,
  domainConfig?: DomainConfig;
  appConfig: AppConfig | undefined;
};

export interface CustomAttributes extends Record<string, string | undefined> {
  page: NotificationPage;
  domain: string | undefined;
  appConfigCode: AppCode | string | undefined;
  initiativeId: string;
  orgId?: string;
  surveyId?: string;
  utrvId?: string;
  dataShareId?: string;
  portfolioId?: string;
  requesterId?: string;
  onboardingId?: string;
  filterByDelegationStatus?: string;
  commentId?: string;
  templateId?: string;
  templateHistoryId?: string;
}

export interface CreateNotificationData<T extends RecipientData = RecipientData> extends CommonNotification {
  _id?: ObjectId;
  title: string;
  content: string;
  recipients: T[];
  category: NotificationCategory;
  topic?: string;
  actionUrl?: string;
  customAttributes?: CustomAttributes;
  overrides?: {
    email?: Partial<OverrideData>
  };
  preferencesOverride?: NotificationCategoryPreference;
}

export interface NotificationModel extends CommonNotification {
  _id: ObjectId;
  recipients: Recipient[]
}

export interface NotificationUser {
  _id: ObjectId;
  email: string;
  firstName?: string;
  surname?: string;
}

interface UserMinWithNotification extends NotificationUser {
  notificationPreferences?: NotificationPreferencesPlain;
}


export interface NotificationRepository {
  create(data: CreateNotificationData): Promise<NotificationModel>
  getSettings(userIds: string[]): Promise<UserMinWithNotification[]>;
  getUserSettings(userId: string | ObjectId): Promise<NotificationPreferencesModel | null>;
}


export interface NotificationDeliveryProvider {
  create(data: NotificationDataExpanded): Promise<unknown>
  getId(): NotificationServices;
}

import { UserPlain } from '../../models/user';
import UserError from '../../error/UserError';
import { InitiativeModel } from '../../models/initiative';
import { CustomerManager, getCustomerManager } from '../payment/CustomerManager';
import { AppConfig } from '../app/AppConfig';
import { defaultMT } from '../app/materiality-tracker';
import { defaultCTPro } from '../app/company-tracker/defaultCTPro';
import { getUnixSeconds, getUTCEndOf, oneDayInSeconds } from '../../util/date';
import {
  SurveyCreateProps,
  getSelfOnboardingManager,
  SelfOnboardingManager,
} from '../onboarding/SelfOnboardingManager';
import { DataPeriods } from '../utr/constants';
import { SurveyScope } from '../survey/SurveyScope';
import {
  getMaterialityMetricGroupService,
  MaterialityMetricGroupService,
} from '../materiality-assessment/MaterialityMetricGroupService';

interface AddCompanyTrackerProps {
  initiative: InitiativeModel;
  user: UserPlain;
  domain?: string;
  appConfig?: AppConfig;
}

export class AppIntegrationService {
  constructor(
    private customerManager: CustomerManager,
    private selfOnboardingManager: SelfOnboardingManager,
    private metricGroupService: MaterialityMetricGroupService
  ) {}

  async addCompanyTracker({ initiative, user, domain, appConfig = defaultCTPro }: AddCompanyTrackerProps) {
    const initiativeId = initiative._id;
    if (initiative.appConfigCode !== defaultMT.code || initiative.permissionGroup !== defaultMT.permissionGroup) {
      throw new UserError('Your organization is not valid Materiality Tracker', {
        initiativeId,
        appConfigCode: initiative.appConfigCode,
        permissionGroup: initiative.permissionGroup,
      });
    }

    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscriptions = subscriptions.filter((sub) =>
      sub.items.some((item) => item.productCode === appConfig.productCode)
    );
    if (integratedSubscriptions.length) {
      throw new UserError('Your organization has already connected Company Tracker', {
        initiativeId,
        subscriptions: integratedSubscriptions.map((sub) => sub.id),
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: appConfig.productCode,
      trialEnd: getUnixSeconds(oneDayInSeconds * 14 + 3600),
    });

    initiative.permissionGroup = appConfig.permissionGroup;
    initiative.appConfigCode = appConfig.code;
    await initiative.save();
    await this.createCTSurvey({ initiative, user, domain });

    return result;
  }

  async addMaterialityTracker({ initiative, user }: { initiative: InitiativeModel; user: UserPlain }) {
    const initiativeId = initiative._id;
    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscription = this.customerManager.getValidSubscriptionByProduct(
      defaultMT.productCode,
      subscriptions
    );
    if (integratedSubscription) {
      throw new UserError('Your organization has already connected Materiality Tracker', {
        initiativeId,
        subscriptions: integratedSubscription.id,
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: defaultMT.productCode,
    });

    return result;
  }

  private async createCTSurvey({
    initiative,
    user,
    domain,
  }: Pick<AddCompanyTrackerProps, 'initiative' | 'user' | 'domain'>) {
    const { metricGroupId, effectiveDate } =
      (await this.metricGroupService.getLatestMetricGroup(initiative, user._id)) ?? {};

    const createSurveyData: SurveyCreateProps = {
      effectiveDate: (effectiveDate ?? getUTCEndOf('month', new Date())).toISOString(),
      period: DataPeriods.Yearly,
      scope: { ...SurveyScope.createEmpty(), custom: metricGroupId ? [metricGroupId] : [] },
      verificationRequired: true,
    };

    await this.selfOnboardingManager.createNewSurvey(initiative, user, { ...createSurveyData, domain });
  }
}

let instance: AppIntegrationService;
export function getAppIntegrationService() {
  if (!instance) {
    instance = new AppIntegrationService(
      getCustomerManager(),
      getSelfOnboardingManager(),
      getMaterialityMetricGroupService()
    );
  }
  return instance;
}

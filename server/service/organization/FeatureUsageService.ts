/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { FeatureCode } from "@g17eco/core";
import { InitiativeRepository, RootInitiativeData } from "../../repository/InitiativeRepository";
import { getRootInitiativeService } from "./RootInitiativeService";
import ContextError from "../../error/ContextError";
import { getInitiativeUserService } from "../user/InitiativeUserService";
import UniversalTracker from "../../models/universalTracker";

interface UsageCheck {
  rootInitiative: RootInitiativeData;
  featureCode: FeatureCode;
}

export interface UsageDetails {
  featureCode: FeatureCode;
  currentUsage: number;
  limit: number;
}

export class FeatureUsageService {

  constructor(
    private rootService: ReturnType<typeof getRootInitiativeService>,
    private initiativeUserService: ReturnType<typeof getInitiativeUserService>,
  ) {
  }

  public async getUsage({ rootInitiative, featureCode }: UsageCheck): Promise<UsageDetails> {

    const config = await this.rootService.getConfig(rootInitiative, {})

    return {
      featureCode,
      limit: config.features.find(f => f.code === featureCode)?.config?.limit ?? 0,
      currentUsage: await this.getFeatureCodeLimit(rootInitiative, featureCode),
    }
  }

  private async getFeatureCodeLimit(
    rootInitiative: RootInitiativeData,
    featureCode: FeatureCode,
  ): Promise<number> {
    switch (featureCode) {
      case FeatureCode.Users:
        return this.getUsersUsage(rootInitiative);
      case FeatureCode.CustomMetrics:
        return this.getCustomerMetrics(rootInitiative);
      case FeatureCode.ReportingLevels:
        return this.getReportingLevelsUsage(rootInitiative);
      default:
        throw new ContextError(`Feature code ${featureCode} usage is not supported`, {
          initiativeId: rootInitiative._id,
          featureCode,
        })
    }
  }

  private async getUsersUsage(rootInitiative: RootInitiativeData): Promise<number> {
    const usage = await this.initiativeUserService.getRootInitiativeUserUsage(rootInitiative._id);
    return usage.total;
  }

  private async getCustomerMetrics(rootInitiative: RootInitiativeData) {
    const initiatives = await InitiativeRepository.getMainTreeChildren(rootInitiative._id);

    // Current and any children counts towards the limit
    return UniversalTracker.find({
      ownerId: {
        $in: initiatives.map(i => i._id),
      }
    }).countDocuments();
  }

  private async getReportingLevelsUsage(rootInitiative: RootInitiativeData) {
    const initiatives = await InitiativeRepository.getMainTreeChildren(rootInitiative._id);
    return initiatives.length;
  }
}

let instance: FeatureUsageService;
export const getFeatureUsageService = () => {
  if (!instance) {
    instance = new FeatureUsageService(
      getRootInitiativeService(),
      getInitiativeUserService(),
    );
  }
  return instance;
}

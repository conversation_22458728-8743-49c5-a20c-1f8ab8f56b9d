/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import {
  getOnboardingManager,
  OnboardingManager,
} from '../onboarding/OnboardingManager';
import Initiative, {
  InitiativeModel,
  InitiativePlain,
  InitiativeTags,
  PERMISSION_GROUPS,
  usageOptions,
} from '../../models/initiative';
import { ObjectId } from 'bson';
import InitiativeGroup from '../../models/initiativeGroup';
import { UserModel } from '../../models/user';
import { InitiativeManager } from '../initiative/InitiativeManager';
import { ObType, OnboardingCreateData } from '../../models/onboarding';
import { UserRoles } from '../user/userPermissions';
import { wwgLogger } from '../wwgLogger';
import { Logger } from 'winston';
import {
  createInitiativeRepository,
  InitiativeRepository,
} from '../../repository/InitiativeRepository';
import moment from "moment";
import { DefaultBlueprintCode } from "../../survey/blueprints";
import { DataPeriods, UtrvType } from "../utr/constants";
import { SurveyScope } from "../survey/SurveyScope";
import MetricGroup, { MetricGroupModel } from "../../models/metricGroup";
import { SurveyImporter } from "../survey/SurveyImporter";
import { blueprintDefaultUnitConfig } from "../units/unitTypes";
import { createStakeholderGroup } from "../stakeholder/StakeholderGroupManager";
import { SurveyModelMinData, SurveyType } from "../../models/survey";
import { createSurveyCode } from "../../util/string";
import { tryGetContext } from '../../middleware/audit/contextMiddleware';
import { MetricGroupManager } from "../metric/MetricGroupManager";

enum OnboardingType {
  Default = 'default',
  CustomMetric = 'custom_metric',
}

interface PortfolioOnboardingData {
  name: string; // Company name
  email: string;
  onboardingType?: OnboardingType;
  effectiveDate?: Date | string;
  customMetricIds?: string[];
  isVerified?: boolean;
  isPremium?: boolean;
  isPublic?: boolean;
  createSurvey?: boolean;
}

interface UserDetails {
  email: string;
  firstName?: string;
  lastName?: string;
}

export interface ImportCompany extends UserDetails {
  companyName: string;
  weight?: number;
  surveyScope?: string[];
  initiativeId?: string;
}

interface PortfolioImportData {
  portfolioId: string;
  companies: ImportCompany[];
}


interface PortfolioOnboardingResult {
  success: boolean,
  company: { _id?: any, name: string }
}

export class PortfolioOnboardingManager {

  constructor(
    private obManager: OnboardingManager,
    private initRepo: InitiativeRepository,
    private logger: Logger
  ) {
  }

  public async processOnboarding(initiative: InitiativePlain, data: PortfolioOnboardingData, user: UserModel) {
    const unverifiedTreeRoot = await this.getUnverifiedTreeRootInitiative();
    const company = await this.createOnboardingCompany(data, unverifiedTreeRoot, user);

    const initiativeGroup = await InitiativeGroup.findById(initiative.initiativeGroupId).orFail().exec();
    initiativeGroup.group.push({ weight: 0, initiativeId: company._id });
    await initiativeGroup.save();
    const result = await this.triggerOnboarding(initiative, company, data, user);

    if (result.failCount > 0) {
      throw new Error(`Failed to create onboarding for user ${data.email}`);
    }

    return company;
  }

  public async onboardCompanies(initiative: InitiativePlain, importData: PortfolioImportData, user: UserModel) {

    const unverifiedTreeRoot = await this.getUnverifiedTreeRootInitiative();
    const initiativeGroup = await InitiativeGroup.findById(initiative.initiativeGroupId).orFail().exec();

    const { companies } = importData;

    const existingCompanies = new Set(initiativeGroup.group.map(g => String(g.initiativeId)));
    const importCompanies: ImportCompany[] = [];

    // Ensure existing companies are not added twice, or being imported twice
    for (const company of companies) {
      if (company.initiativeId && !existingCompanies.has(company.initiativeId)) {
        importCompanies.push(company);
        existingCompanies.add(company.initiativeId);
      }
    }

    const results = await Promise.all(importCompanies.map(data => {
      return this.processCompany(initiative, data, unverifiedTreeRoot, user);
    }));

    for (const { company, success } of results) {
      if (success && company._id) {
        const weight = importCompanies.find(c => c.initiativeId === String(company._id))?.weight ?? 0;
        initiativeGroup.group.push({ weight, initiativeId: company._id });
      }
    }
    await initiativeGroup.save();

    return results;
  }

  private async triggerOnboarding(
    portfolio: InitiativePlain,
    company: InitiativeModel,
    userDetails: UserDetails & PortfolioOnboardingData,
    user: UserModel
  ) {
    const context = tryGetContext();
    const obData: OnboardingCreateData = {
      type: ObType.Initiative,
      metadata: {
        domain: context?.origin,
        app: company.permissionGroup,
      },
      user: {
        complete: false,
        permissions: [{
          initiativeId: company._id,
          permissions: [UserRoles.Manager],
        }],
        emailTemplate: this.obManager.getEmailTemplate([UserRoles.Manager]),
        email: userDetails.email,
        firstName: userDetails.firstName,
        surname: userDetails.lastName,
      },
      initiativeId: company._id,
      createdBy: user._id,
    };

    // Only staff - Add survey config with custom metrics
    if (user.isStaff
      && userDetails.onboardingType === OnboardingType.CustomMetric
      && userDetails.customMetricIds
      && userDetails.effectiveDate) {

      const metricGroups = await MetricGroup.find({
        _id:  userDetails.customMetricIds.map(id => new ObjectId(id)),
        initiativeId: portfolio._id
      });

      if (metricGroups.length !== 0) {
        return await this.customMetricOnboarding(
          metricGroups,
          company,
          obData,
          userDetails,
          user,
        );
      }
    }

    return this.obManager.onboardPortfolioInitiative(obData);
  }

  private async customMetricOnboarding(
    metricGroups: MetricGroupModel[],
    company: InitiativeModel,
    obData: OnboardingCreateData,
    userDetails: UserDetails & PortfolioOnboardingData,
    user: UserModel,
  ) {
    await MetricGroupManager.shareGroups(metricGroups, company);

    if (userDetails.createSurvey) {

      const effectiveDate = moment(userDetails.effectiveDate).toDate();
      const createData: SurveyModelMinData = {
        type: SurveyType.Default,
        visibleUtrvs: [],
        code: createSurveyCode(company.code),
        name: undefined,
        sourceName: DefaultBlueprintCode,
        period: DataPeriods.Yearly,
        effectiveDate: effectiveDate,
        utrvType: UtrvType.Actual,
        visibleStakeholders: [],
        unitConfig: blueprintDefaultUnitConfig,
        initiativeId: company._id,
        stakeholders: createStakeholderGroup([], []),
        roles: { admin: [], viewer: [] },
        evidenceRequired: true,
        verificationRequired: true,
        scope: {
          ...SurveyScope.createEmpty(),
          custom: metricGroups.map(metricGroup => metricGroup._id),
        },
      };

      const newSurvey = await SurveyImporter.create(createData, user);

      // Add user onboarding to join survey as admin/stakeholders
      obData.surveyRoles = { admin: [newSurvey._id], viewer: [] }
      obData.surveyStakeholders = createStakeholderGroup([newSurvey._id], [newSurvey._id])

      return this.obManager.onboardPortfolioInitiative(obData);
    }

    // Normal use with survey onboarding
    obData.surveyConfig = {
      code: createSurveyCode(company.code),
      complete: false,
      period: DataPeriods.Yearly,
      effectiveDate: moment(userDetails.effectiveDate).toDate(),
      evidenceRequired: true,
      verificationRequired: true,
      sourceName: DefaultBlueprintCode,
      utrvType: UtrvType.Actual,
      scope: {
        ...SurveyScope.createEmpty(),
        custom: metricGroups.map(metricGroup => metricGroup._id),
      },
    }

    return this.obManager.onboardPortfolioInitiative(obData);
  }

  private async processCompany(
    portfolio: InitiativePlain,
    data: ImportCompany,
    unverifiedTreeRoot: InitiativePlain,
    user: UserModel,
  ): Promise<PortfolioOnboardingResult> {
    try {

      if (data.initiativeId) {
        const company =  await this.initRepo.mustFindById(data.initiativeId);
        // Existing company, just add to portfolio (no user invitation)
        return { company, success: true }
      }

      const obData = { ...data, name: data.companyName };
      const company = await this.createOnboardingCompany(obData, unverifiedTreeRoot, user);
      const result = await this.triggerOnboarding(portfolio, company, obData, user);
      return { company, success: result.failCount === 0 }
    } catch (e) {
      this.logger.error(e)
      return { company: { name: data.companyName }, success: false }
    }
  }

  private async createOnboardingCompany(
    data: PortfolioOnboardingData,
    unverifiedTreeRoot: InitiativePlain,
    user: UserModel,
  ) {
    const tags = [InitiativeTags.Organization];
    if (user.isStaff && data.isVerified) {
      tags.push(InitiativeTags.VerifiedOrganization);
    }

    return InitiativeManager.create({
      code: `/companies/${(new ObjectId())}`,
      name: data.name,
      tags: tags,
      parentId: unverifiedTreeRoot._id,
      permissionGroup: user.isStaff && data.isPremium ? PERMISSION_GROUPS.COMPANY_TRACKER_PRO : PERMISSION_GROUPS.FREE,
      // isPublic: user.isStaff && data.isPublic,
      usage: usageOptions,
    });
  }

  private async getUnverifiedTreeRootInitiative() {
    const unverifiedTreeRoot = await Initiative.findOne({ code: '/companies/unverified' });
    if (!unverifiedTreeRoot) {
      throw new Error('Root initiative with code /companies/unverified does not exist.');
    }
    return unverifiedTreeRoot;
  }
}

let instance: PortfolioOnboardingManager;
export const getPortfolioOnboardingManager = () => {
  if (!instance) {
    instance = new PortfolioOnboardingManager(
      getOnboardingManager(),
      createInitiativeRepository(),
      wwgLogger,
    );
  }
  return instance;
}

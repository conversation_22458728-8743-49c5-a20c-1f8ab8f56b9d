import { PortfolioRepository } from '../../repository/PortfolioRepository';
import { ObjectId } from 'bson';
import UniversalTracker, { UniversalTrackerPlain } from '../../models/universalTracker';
import { getGroup, Group, standards } from '@g17eco/core';
import { frameworkTags } from '../../models/common/universalTrackerTags';
import { ActionList } from '../utr/constants';
import { NotApplicableTypes, UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { DownloadScope } from '../survey/scope/downloadScope';
import { getPreferredName, getTypeCode } from "../utr/utrUtil";
import { getBlueprintRepository } from '../../repository/BlueprintRepository';
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import { DelegationScope, Scope, SurveyModelPlain } from '../../models/survey';
import { SurveyScope } from "../survey/SurveyScope";
import { ScopeValidationResult } from "../share/dataShareUtil";
import { InitiativePlain } from "../../models/initiative";
import { PackUsageQuery, PortfolioMetricGroup, PortfolioService } from "./PortfolioService";
import { KeysEnum } from "../../models/commonProperties";
import ContextError from "../../error/ContextError";
import { filterByFramework, filterByStandard, filterByStandardTypeCode } from "../survey/scope/filterScope";
import { getDataShareRepository } from '../../repository/DataShareRepository';
import { RequesterType } from '../../models/dataShare';

interface ScopePackStats {
  isPrivate: number;
  na: number;
  nr: number;
  created: number;
  updated: number;
  rejected: number;
  verified: number;
  totalUtrvs: number;
}

interface SurveyCounter {
  surveyCount: number;
  initiativeUniqueCount: number;
  scopeType: keyof DelegationScope;
}

export interface ScopePackRowData extends ScopePackStats, SurveyCounter {
  name: string;
  code: string;
}

type InitialPackRowData = Pick<ScopePackRowData, 'code' | 'name' | 'scopeType' | 'surveyCount' | 'initiativeUniqueCount'>
type UtrvInfo = Pick<UniversalTrackerValuePlain, 'universalTrackerId' | 'status' | 'isPrivate' | 'valueData'>;

export interface FilterOptions {
  initiativeIds: ObjectId[];
  startDate?: string;
  endDate?: string;
  validatedScope: ScopeValidationResult;
}

export interface BreakdownFilter extends FilterOptions {
  customMetricGroups: PortfolioMetricGroup[];
}

type GroupFilter = Pick<Group, 'code' | 'name'>;

interface DrilldownFilters {
  type: keyof Scope,
  groupCode: string;
  subGroupCode: string | undefined;
  leafGroupCode: string | undefined;
  leafChildGroupCode: string | undefined;
  group: GroupFilter;
  selectedGroups?: GroupFilter[];
}

interface DrilldownSelectedGroups extends DrilldownFilters {
  selectedGroups: GroupFilter[];
}

interface DrillingFilterOptions<T extends DrilldownFilters = DrilldownFilters> extends BreakdownFilter {
  drilldownFilters: T
}

interface CountUsesPerPack {
  uniqueCount: Map<string, Set<string>>;
  surveyCountPerPack: Map<string, SurveyCounter>;
}

type CountPackUtr = Pick<UniversalTrackerPlain, '_id' | 'name' | 'type' | 'alternatives' | 'tags' | 'typeTags' | 'typeCode'>;
const countPackUtrProjection: KeysEnum<CountPackUtr, 1> = {
  _id: 1,
  name: 1,
  alternatives: 1,
  tags: 1,
  type: 1,
  typeCode: 1,
  typeTags: 1
}

interface CountPackDataByUtrIdParams extends CountUsesPerPack {
  drilldownFilters?: DrilldownFilters;
  customMetricGroups: PortfolioMetricGroup[];
  utrs: CountPackUtr[];
  countPerUtrId: Map<string, ScopePackStats | undefined>;
}

interface CountPerSurvey extends CountUsesPerPack {
  countPerUtrId: Map<string, ScopePackStats | undefined>;
  surveyCount: Map<string, Set<string>>;
}

interface CountPerLookupCheckParams extends CountUsesPerPack {
  initiativeId: string;
  survey: Pick<SurveyModelPlain, 'scope'>;
  lookupChecks: { codes: string[]; key: keyof Scope }[];
}

interface CountPerUtrParams {
  survey: Pick<SurveyModelPlain, '_id' | 'initiativeId'> & { utrvs: [] };
  countPerUtrId: Map<string, ScopePackStats | undefined>;
  surveyCount?: Map<string, Set<string>>;
  uniqueCount?: Map<string, Set<string>>;
}

export interface LookupChecksType {
  key: keyof Scope;
  codes: string[];
}

type PortfolioSurvey = Pick<SurveyModelPlain, '_id' | 'scope' | 'initiativeId'> & { utrvs: [] };

interface PackUsageResponse {
  packs: ScopePackRowData[],
}

interface ScopeGroupNameLookup {
  scopeType: keyof Scope;
  scopeCode: string;
  customMetricGroups: PortfolioMetricGroup[];
  selectedGroups: GroupFilter[] | undefined;
}

interface GroupLookup {
  customMetricGroups: PortfolioMetricGroup[]
  groupCode: string;
  subGroupCode: string | undefined;
  leafGroupCode: string | undefined;
  leafChildGroupCode: string | undefined;
}

type UtrPackFilters = { $or: { [k: string]: unknown }[] } | undefined;

interface UtrInScope {
  scopeType: keyof Scope;
  utr: CountPackUtr;
  scopeCode: string;
  groupUtrIds: string[];
}

interface UtrInSubGroup extends Omit<UtrInScope, 'groupUtrIds'>{
  group: string;
}

export class PortfolioPackUsageService {

  public static async getScopeBreakdown(filterOptions: BreakdownFilter) {

    // Filter based on scope match, as it might have been restricted by validatedScope
    const utrFilter = await DownloadScope.generateMultiScopeMatch(filterOptions.validatedScope, '', '_id');

    // Only load those restricted utrs, therefore it will restrict surveys utrvs as well
    const utrs = await this.getPackUsageUtrs(utrFilter);

    // Filter surveys based on *survey.scope* properties visibleUtrvs are not yet filtered
    const surveys = await PortfolioRepository.getPortfolioSurveys(filterOptions, utrs.map(utr => utr._id));

    // Generate the grouping either by filter only or all available possible
    const lookupChecks = this.getScopeLookupChecks(filterOptions);

    const { surveyCountPerPack, countPerUtrId, uniqueCount } = this.countByStatusPerUtrId(surveys, lookupChecks);

    return this.getPackDataByUtrId({
      surveyCountPerPack,
      utrs,
      countPerUtrId,
      uniqueCount,
      customMetricGroups: filterOptions.customMetricGroups,
    });
  }

  public static async getSubGroupBreakdown(filterOptions: DrillingFilterOptions<DrilldownSelectedGroups>) {

    const { group, type, selectedGroups } = filterOptions.drilldownFilters;
    const subGroupsCodes = selectedGroups.map(g => g.code);
    const utrFilter = await DownloadScope.generateSubgroupMatch({
      group: group.code,
      subGroups: subGroupsCodes
    });
    const utrs = await this.getPackUsageUtrs(utrFilter);

    const surveys = await PortfolioRepository.getPortfolioSurveys(filterOptions, utrs.map((utr) => utr._id));

    const lookupChecks = [{ key: type, codes: subGroupsCodes }];

    const { surveyCountPerPack, countPerUtrId, uniqueCount } = this.countByStatusPerUtrId(surveys, lookupChecks);

    return this.getPackDataByUtrId({
      surveyCountPerPack,
      utrs,
      drilldownFilters: filterOptions.drilldownFilters,
      customMetricGroups: filterOptions.customMetricGroups,
      countPerUtrId,
      uniqueCount
    });
  }

  /** Display question breakdown for level 2 subgroups or level 3 subgroups **/
  public static async getQuestionBreakdown(filterOptions: DrillingFilterOptions) {
    const utrFilter = await this.getQuestionFilter(filterOptions);

    const utrs = await this.getPackUsageUtrs(utrFilter);
    const surveys = await PortfolioRepository.getPortfolioSurveys(filterOptions, utrs.map((utr) => utr._id));

    const packsData: ScopePackRowData[] = [];
    const { groupCode } = filterOptions.drilldownFilters;

    const utrNameMap = new Map(utrs.map(utr => {
      const id = utr._id.toString();
      return [id, getTypeCode(utr, [groupCode]) || getPreferredName(utr, [groupCode])];
    }))

    // Grouping per utrId, status, na, nr, and unique surveyIds, initiativeIds
    const { surveyCount, countPerUtrId, uniqueCount }: CountPerSurvey = surveys.reduce(
      (acc, survey) => {
        return this.surveyCountByStatusPerUtr({
          survey,
          countPerUtrId: acc.countPerUtrId,
          uniqueCount: acc.uniqueCount,
          surveyCount: acc.surveyCount,
        });
      },
      {
        surveyCount: new Map<string, Set<string>>(),
        countPerUtrId: new Map<string, ScopePackStats | undefined>(),
        uniqueCount: new Map<string, Set<string>>(),
      }
    );

    // Converting to expected RowData with survey, initiative counts
    countPerUtrId.forEach((counter, utrId) => {
      packsData.push({
        name: utrNameMap.get(utrId) ?? utrId,
        code: utrId,
        ...counter,
        surveyCount: surveyCount.get(utrId)?.size ?? 0,
        initiativeUniqueCount: uniqueCount.get(utrId)?.size ?? 0,
      } as ScopePackRowData);
    });

    return {
      packs: packsData,
    };
  }

  private static async getQuestionFilter(filterOptions: DrillingFilterOptions) {
    const { selectedGroups, group, subGroupCode, leafGroupCode, leafChildGroupCode } = filterOptions.drilldownFilters;

    // No group to filter by, showing questions for top level
    if (!selectedGroups || selectedGroups.length === 0) {

      const subGroupFilter = leafChildGroupCode || leafGroupCode || subGroupCode;
      if (subGroupFilter) {
        // No longer have subgroups, need to filter by available subGroup code
        return DownloadScope.generateSubgroupMatch({
          group: group.code,
          subGroups: [subGroupFilter]
        });
      }

      // Otherwise, we are filtering by top level scope field
      return DownloadScope.generateMultiScopeMatch(filterOptions.validatedScope, '', '_id');
    }

    // Only deals with standards/frameworks? Seems ok for now, until SDG needs to be supported
    return DownloadScope.generateSubgroupMatch({
      group: group.code,
      subGroups: selectedGroups.map(g => g.code)
    });
  }

  /**
   *  Group data based on the scopeType scopeCode
   *
   *  Example:
   *  [{ code: 'gri', name: 'GRI 2021', type}]
   * **/
  private static getPackDataByUtrId = ({
    surveyCountPerPack,
    utrs,
    countPerUtrId,
    uniqueCount,
    drilldownFilters,
    customMetricGroups,
  }: CountPackDataByUtrIdParams) => {
    const packsData: ScopePackRowData[] = [];

    const { group, selectedGroups = [] } = drilldownFilters ?? {};

    // scopeCode here represent top level scope group, standard, framework or custom
    // like gri | tcfd | customGroupId etc.
    surveyCountPerPack.forEach((counterObject: SurveyCounter, scopeCode: string) => {

      const scopeType = counterObject.scopeType;

      const packData = this.getInitialPackData({
        code: scopeCode,
        scopeType,
        name: this.getScopeGroupName({
          scopeType,
          scopeCode,
          customMetricGroups,
          selectedGroups,
        }),
        surveyCount: counterObject.surveyCount,
        initiativeUniqueCount: uniqueCount.get(scopeCode)?.size ?? 0,
      });

      // If we're trying to group by metric group, find the group and extract utrIds **only** for that group
      const groupUtrIds: string[] = scopeType === 'custom' ?
        customMetricGroups.find(g => g._id.toString() === scopeCode)?.universalTrackers.map(String) ?? [] :
        [];

      utrs.forEach((utr) => {
        // Group here, means we are in drilldown of group view and listing subgroups
        const shouldAdd = group ?
          this.isUtrInSubGroup({ utr, scopeCode, group: group.code, scopeType }) :
          this.isUtrInScope({ utr, scopeCode, groupUtrIds, scopeType });

        if (shouldAdd) {
          const packCounter = countPerUtrId.get(utr._id.toString());
          if (packCounter) {
            Object.entries(packCounter).forEach(([key, value]) => {
              packData[key as keyof typeof packCounter] += value;
            });
          }
        }
      });
      packsData.push(packData);
    });
    return { packs: packsData };
  };

  /**
   * Does not support sdg? I assume it's expected, as we do not want to
   * display breakdowns for SDGs yet.
   *
   * @Example: scopes: ['gri], will lead to scope with { standards: ['gri'], ... }
   * In this case we only want to generate lookups for 'gri' key,
   * all the other ones are skip.
   **/
  private static getScopeLookupChecks(
    { validatedScope, customMetricGroups }: Pick<BreakdownFilter, 'validatedScope' | 'customMetricGroups'>
  ): LookupChecksType[] {

    const scope = validatedScope.scope;

    // Trying to apply grouping by everything in the query
    if (SurveyScope.isEmpty(scope)) {
      return [
        { key: 'standards', codes: Object.keys(standards), },
        { key: 'frameworks', codes: frameworkTags },
        { key: 'custom', codes: customMetricGroups.map(mg => mg._id.toString()) }
      ]
    }

    // Otherwise this must the requested scope only
    return [
      { key: 'standards', codes: scope.standards ?? [], },
      { key: 'frameworks', codes: scope.frameworks ?? [] },
      { key: 'custom', codes: scope.custom ?? [] }
    ];
  }

  private static async getPackUsageUtrs(utrFilter: UtrPackFilters): Promise<CountPackUtr[]> {
    const blueprintRepo = getBlueprintRepository();
    const blueprint = await blueprintRepo.mustFindExpandedByCode(DefaultBlueprintCode);
    const utrCodes = extractVisibleUtrCodes(blueprint);
    return  UniversalTracker.find(
      { code: { $in: utrCodes }, ...utrFilter },
      countPackUtrProjection
    )
      .lean()
      .exec();
  }

  /**
   * Based on the returned survey utrvs,
   * 1. group the counts for the surveys and initiative only
   * 2. Group per utr only
   */
  private static countByStatusPerUtrId = (surveys: PortfolioSurvey[], lookupChecks: LookupChecksType[]) => {
    return surveys.reduce(
      (acc, survey) => {
        const initiativeId = survey.initiativeId.toString();
        const { surveyCountPerPack, uniqueCount } = this.surveyCountUsesPerPack({
          lookupChecks,
          survey,
          uniqueCount: acc.uniqueCount,
          initiativeId,
          surveyCountPerPack: acc.surveyCountPerPack,
        });
        const { countPerUtrId } = this.surveyCountByStatusPerUtr({
          survey,
          countPerUtrId: acc.countPerUtrId
        });
        return { surveyCountPerPack, countPerUtrId, uniqueCount };
      },
      {
        surveyCountPerPack: new Map<string, SurveyCounter>(),
        countPerUtrId: new Map<string, ScopePackStats | undefined>(),
        uniqueCount: new Map<string, Set<string>>(),
      }
    );
  };

  private static surveyCountUsesPerPack(options: CountPerLookupCheckParams) {
    const { lookupChecks, survey, uniqueCount, initiativeId, surveyCountPerPack } = options;

    lookupChecks.forEach((lookupCheck) => {
      const scopeType = lookupCheck.key;
      if (!survey.scope?.[scopeType]) {
        return; // Survey must have added the whole key (seems to be the logic now
      }

      for (const key of survey.scope[scopeType]) {
        // gri, tcfd, id based on the lookup key (aka scopeType
        const scopeCode = key.toString();
        if (lookupCheck.codes.includes(scopeCode)) {
          const existing = uniqueCount.get(scopeCode);
          existing ? existing.add(initiativeId) : uniqueCount.set(scopeCode, new Set([initiativeId]));

          const packCounter = surveyCountPerPack.get(scopeCode);
          packCounter
            ? (packCounter.surveyCount += 1)
            : surveyCountPerPack.set(scopeCode, {
              surveyCount: 1,
              initiativeUniqueCount: 1,
              scopeType,
            });
        }
      }
    });
    return { surveyCountPerPack, uniqueCount };
  }

  /** Accumulate survey data per utr id **/
  private static surveyCountByStatusPerUtr = ({
    survey,
    countPerUtrId,
    surveyCount,
    uniqueCount,
  }: CountPerUtrParams) => {
    survey.utrvs.forEach((utrv: UtrvInfo) => {
      const utrId = utrv.universalTrackerId.toString();
      const packCounter = countPerUtrId.get(utrId);
      const uniqueSurveyCount = surveyCount?.get(utrId);
      const uniqueInitiativeCount = uniqueCount?.get(utrId);
      if (packCounter) {
        packCounter.totalUtrvs += 1;
        packCounter.na += utrv.valueData?.notApplicableType === NotApplicableTypes.NA ? 1 : 0;
        packCounter.nr += utrv.valueData?.notApplicableType === NotApplicableTypes.NR ? 1 : 0;
        packCounter.created += utrv.status === ActionList.Created ? 1 : 0;
        packCounter.updated += utrv.status === ActionList.Updated ? 1 : 0;
        packCounter.rejected += utrv.status === ActionList.Rejected ? 1 : 0;
        packCounter.verified += utrv.status === ActionList.Verified ? 1 : 0;
        packCounter.isPrivate += utrv.isPrivate ? 1 : 0;
        if (uniqueSurveyCount) {
          uniqueSurveyCount.add(survey._id.toString());
        }
        if (uniqueInitiativeCount) {
          uniqueInitiativeCount.add(survey.initiativeId.toString());
        }
        return { countPerUtrId, surveyCount: uniqueSurveyCount, uniqueCount: uniqueInitiativeCount };
      }
      if (surveyCount) {
        surveyCount.set(utrId, new Set([survey._id.toString()]));
      }
      if (uniqueCount) {
        uniqueCount.set(utrId, new Set([survey.initiativeId.toString()]));
      }
      countPerUtrId.set(utrId, {
        totalUtrvs: 1,
        na: utrv.valueData?.notApplicableType === NotApplicableTypes.NA ? 1 : 0,
        nr: utrv.valueData?.notApplicableType === NotApplicableTypes.NR ? 1 : 0,
        created: utrv.status === ActionList.Created ? 1 : 0,
        updated: utrv.status === ActionList.Updated ? 1 : 0,
        rejected: utrv.status === ActionList.Rejected ? 1 : 0,
        verified: utrv.status === ActionList.Verified ? 1 : 0,
        isPrivate: utrv.isPrivate ? 1 : 0,
      });
    });

    return { countPerUtrId, surveyCount, uniqueCount };
  };

  // scopeCode is top level standard or framework: tcfd, ungc, gri, gri2021... etc.
  private static isUtrInScope = ({ utr, scopeCode, scopeType, groupUtrIds }: UtrInScope) => {
    switch (scopeType) {
      case 'standards':
        return filterByStandard([scopeCode], utr.type, utr.typeTags, utr.alternatives);
      case 'frameworks':
        return (utr.tags?.[scopeCode] || []).length > 0;
      case 'custom':
        return groupUtrIds.includes(utr._id.toString());
      default:
        throw new ContextError(`Filtering by ${scopeType} is not supported in isUtrInScope fn`, {
          scopeCode,
          scopeType,
        });
    }
  };

  private static isUtrInSubGroup = ({ utr, scopeCode, scopeType, group }: UtrInSubGroup) => {
    switch (scopeType) {
      case "standards":
        return filterByStandardTypeCode({ utr, standardCode: group, typeTag: scopeCode });
      case "frameworks":
        return filterByFramework([scopeCode], utr.tags);
      default:
        throw new ContextError(`Filtering by ${scopeType} is not supported in isUtrInSubGroup fn`, {
          scopeCode,
          scopeType,
        });
    }
  };

  private static getInitialPackData({code, name, scopeType, surveyCount, initiativeUniqueCount}: InitialPackRowData): ScopePackRowData {
    return {
      code,
      name,
      scopeType,
      created: 0,
      isPrivate: 0,
      na: 0,
      nr: 0,
      rejected: 0,
      totalUtrvs: 0,
      surveyCount,
      initiativeUniqueCount,
      updated: 0,
      verified: 0,
    };
  }

  /**
   * Confusing part about all of this is how we do
   * scope filters vs group view vs group drilldown
   */
  public static async processRequest(
    portfolio: InitiativePlain,
    query: PackUsageQuery,
    maxRecursion: number,
  ) {
    const { startDate, endDate, sector, industry, group, subGroup, leafGroup, leafChildGroup, scopeGroups } = query;

    const companies = await PortfolioService.getRecursiveInitiatives(
      portfolio,
      maxRecursion,
      { sector, industry }
    );

    const repo = getDataShareRepository();
    const activeDataShares = await repo.findActiveDataShare({
      requesterType: RequesterType.Portfolio,
      requesterId: portfolio._id,
      initiativeId: {
        $in: Array.from(companies.keys()).map((id) => new ObjectId(id))
      },
    });

    // Passing in group we are in group view, therefore that is our scope
    const scope = group ? SurveyScope.fromCodes([group]) : SurveyScope.fromScopeGroups(scopeGroups);

    // get all the metric groups if none selected, so we return all assigned companies by default
    const customMetricGroups = await PortfolioService.getPortfolioMetricGroups(portfolio, scope.custom);

    const companiesByScope = await PortfolioService.getSharedScopesCompanies({
      requestedScope: { scope },
      activeDataShares,
    });

    if (!group) {
      const resp: PackUsageResponse = { packs: [] };
      for (const grouping of Object.values(companiesByScope)) {
        const packs = await PortfolioPackUsageService.getScopeBreakdown({
          initiativeIds: grouping.initiativeIds,
          validatedScope: grouping.validatedScope,
          customMetricGroups,
          startDate,
          endDate
        });
        // Need group the packs based on the codes again.
        resp.packs.push(...packs.packs);
      }
      return this.mergePacks(resp);
    }

    // At this point companies must have access to the top level group code
    const drilldownFilters  = this.getDrilldownFilters({
      groupCode: group,
      subGroupCode: subGroup,
      leafGroupCode: leafGroup,
      leafChildGroupCode: leafChildGroup,
      customMetricGroups
    });
    if (drilldownFilters.selectedGroups) {
      const resp: PackUsageResponse = { packs: [] }
      for (const grouping of Object.values(companiesByScope)) {
        const data = await PortfolioPackUsageService.getSubGroupBreakdown({
          initiativeIds: grouping.initiativeIds,
          validatedScope: grouping.validatedScope,
          drilldownFilters: drilldownFilters as DrilldownSelectedGroups,
          customMetricGroups,
          startDate,
          endDate,
        });
        // Need group the packs based on the codes again.
        resp.packs.push(...data.packs);
      }
      return this.mergePacks(resp);
    }

    const resp: PackUsageResponse = { packs: [] }
    for (const grouping of Object.values(companiesByScope)) {
      const data = await PortfolioPackUsageService.getQuestionBreakdown({
        initiativeIds: grouping.initiativeIds,
        validatedScope: grouping.validatedScope,
        drilldownFilters,
        customMetricGroups,
        startDate,
        endDate,
      });
      // Need group the packs based on the codes again.
      resp.packs.push(...data.packs);
    }
    return this.mergePacks(resp);
  }

  private static async mergePacks(resp: PackUsageResponse) {

    const packs = resp.packs.reduce((acc, currentValue) => {

      const current = acc.get(currentValue.code);

      if (current) {
        // ScopePackStats part
        current.totalUtrvs += currentValue.totalUtrvs;
        current.na += currentValue.na;
        current.nr += currentValue.nr;
        current.created += currentValue.created;
        current.updated += currentValue.updated;
        current.rejected += currentValue.rejected;
        current.verified += currentValue.verified;
        current.isPrivate += currentValue.isPrivate;

        // SurveyCounter
        current.surveyCount += currentValue.surveyCount;
        current.initiativeUniqueCount += currentValue.initiativeUniqueCount;
      } else {
        acc.set(currentValue.code, currentValue)
      }


      return acc;
    }, new Map<string, ScopePackRowData>());

    return {
      ...resp,
      packs: Array.from(packs.values()),
    };
  }

  private static getScopeGroupName({ scopeType, scopeCode, customMetricGroups, selectedGroups = [] }: ScopeGroupNameLookup) {

    if (scopeType === 'custom') {
      return customMetricGroups.find(g => g._id.toString() === scopeCode)?.groupName ?? scopeCode;
    }

    if (selectedGroups.length > 0) {
      // Must be in drilldown stage. We should be grouping by these groups
      return selectedGroups.find(g => g.code === scopeCode)?.name ?? scopeCode;
    }

    return getGroup(scopeType, scopeCode)?.name ?? scopeCode;
  }

  /**
   *  get the latest subgroups of scopeUrl: .../group/subGroup/leafGroup/leafChildGroup
   *  leafChildGroup is the last level of group, so there is no subgroups,
   *  which will lead to generate questions instead
   */
  private static getDrilldownFilters(lookup: GroupLookup): DrilldownFilters {
    const { groupCode, subGroupCode, leafGroupCode, leafChildGroupCode, customMetricGroups } = lookup;

    const type = SurveyScope.getScopeType(groupCode) as (keyof Scope | '');
    if (!type) {
      throw new ContextError(`Failed to verify group scope type '${groupCode}'`, {
        groupCode,
        subGroupCode,
        leafGroupCode,
        leafChildGroupCode
      });
    }

    if (type === 'custom') {
      const group = customMetricGroups.find(g => g._id.toString() === groupCode);
      if (!group) {
        throw new ContextError(`Failed to custom metric group by code ${groupCode}`, {
          groupIds: customMetricGroups.map(g => g._id.toString()),
          groupCode,
        })
      }

      return {
        type,
        groupCode,
        subGroupCode,
        leafGroupCode,
        leafChildGroupCode,
        // We know metric groups don't have subgroups
        group: { code: groupCode, name: group.groupName },
      }
    }

    const group = getGroup('standards-and-frameworks', groupCode);
    if (!group) {
      throw new ContextError(`Failed to resolve group by code ${groupCode}`, {
        groupCode,
        subGroupCode,
        leafGroupCode,
        leafChildGroupCode
      })
    }

    return {
      type,
      groupCode,
      subGroupCode,
      leafGroupCode,
      leafChildGroupCode,

      // Entry levels
      group,
      selectedGroups: this.getSelectedGroups({leafChildGroupCode, leafGroupCode, subGroupCode, group}),
    }
  }

  private static getSelectedGroups({
    leafChildGroupCode,
    leafGroupCode,
    subGroupCode,
    group,
  }: Pick<GroupLookup, 'leafChildGroupCode' | 'leafGroupCode' | 'subGroupCode'> & {
    group: Group;
  }): Group[] | undefined {
    if (!subGroupCode) {
      return group.subgroups;
    }

    const leafGroups = group.subgroups?.find((item) => item.code == subGroupCode)?.subgroups;
    if (!leafGroupCode || !leafGroups) {
      return leafGroups;
    }

    const leafChildGroups = leafGroups.find((item) => item.code == leafGroupCode)?.subgroups;
    if (!leafChildGroupCode || !leafChildGroups) {
      return leafChildGroups;
    }

    // leafChild will never have subgroups, but try anyway
    return leafChildGroups.find((item) => item.code == leafChildGroupCode)?.subgroups;
  }
}

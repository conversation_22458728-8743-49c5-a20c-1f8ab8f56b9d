import { ObjectId } from 'bson';
import { SurveyModelPlain, SurveyPermission, SurveyPermissionType } from '../../models/survey';
import { Workgroup } from '../../models/workgroup';
import { DataScopeAccess } from '../../models/dataShare';
import { SurveyUserRoles } from '../../types/roles';
import { getSurveyWorkgroupService } from './SurveyWorkgroupService';
import { ToGetWorkgroupsSurvey } from '../../types/workgroup';
import { ScopeUtrv, ToFetchUtrScopeUtrv } from '../../util/scope-utrv';
import { ScopeFiltersUtr, scopeFiltersUtrProjection } from '../survey/scope/filterScope';
import UniversalTracker from '../../models/universalTracker';
import ContextError from '../../error/ContextError';

export class WorkgroupPermissions {
  constructor(
    private workgroupModel: typeof Workgroup,
    private surveyWorkgroupService: ReturnType<typeof getSurveyWorkgroupService>,
    private utrModel: typeof UniversalTracker
  ) {}

  async checkHasSurveyUserRoles({
    survey,
    userId,
    roles,
  }: {
    survey: Pick<SurveyModelPlain, 'permissions'>;
    userId: ObjectId;
    roles: SurveyUserRoles[];
  }) {
    const workgroupIds = survey.permissions
      ?.filter(
        (permission) =>
          this.checkIsSurveyWorkgroupPermission({
            permission,
            access: DataScopeAccess.Full,
            roles,
          }) ||
          // Partial access is considered for Stakeholder and Verifier roles only, so we count 'Partial' access Admin role if included. Ignore None access for now.
          this.checkIsSurveyWorkgroupPermission({
            permission,
            access: DataScopeAccess.Partial,
            roles: roles.filter((r) => r === SurveyUserRoles.Admin),
          })
      )
      .map((permission) => permission.modelId);

    if (!workgroupIds || !workgroupIds.length) {
      return false;
    }

    const found = await this.workgroupModel.exists({
      _id: { $in: workgroupIds },
      'users._id': userId,
    });

    return Boolean(found);
  }

  async checkHasUtrvsUserRoles({
    utrvs,
    userId,
    roles,
    survey,
  }: {
    utrvs: ToFetchUtrScopeUtrv[];
    userId: ObjectId;
    roles: (SurveyUserRoles.Stakeholder | SurveyUserRoles.Verifier)[];
    survey: ToGetWorkgroupsSurvey;
  }): Promise<boolean> {
    if (!utrvs.length) {
      return false;
    }

    if (!survey?.permissions) {
      return false;
    }

    // Consider utrvs come from different sources and might not have utr, we need to fetch them here when needed only.
    const utrvsWithUtr = await this.mapUtrToUtrvs(utrvs);

    const workgroupIds = survey.permissions
      .filter((permission) =>
        this.checkIsSurveyWorkgroupPermission({ permission, access: DataScopeAccess.Partial, roles })
      )
      .map((permission) => permission.modelId);

    const surveyWorkgroups = await this.surveyWorkgroupService.getWorkgroups({
      survey,
      utrvs: utrvsWithUtr,
      workgroupIds,
      userIds: [userId],
    });
    if (!surveyWorkgroups.length) {
      return false;
    }

    const allowedUtrvIds = new Set<string>();
    for (const surveyWorkgroup of surveyWorkgroups) {
      surveyWorkgroup.utrvIds.forEach((utrvId) => allowedUtrvIds.add(utrvId.toString()));
    }

    return utrvs.every((utrv) => allowedUtrvIds.has(utrv._id.toString()));
  }

  private async mapUtrToUtrvs(utrvs: ToFetchUtrScopeUtrv[]): Promise<ScopeUtrv[]> {
    const toFetchUtrIds = utrvs.reduce((ids, utrv) => {
      if (!utrv.universalTracker) {
        ids.add(utrv.universalTrackerId.toString());
      }
      return ids;
    }, new Set<string>());

    if (!toFetchUtrIds.size) {
      return utrvs as ScopeUtrv[];
    }

    const utrs = await this.utrModel
      .find({ _id: { $in: Array.from(toFetchUtrIds).map((id) => new ObjectId(id)) } }, scopeFiltersUtrProjection)
      .lean<ScopeFiltersUtr[]>()
      .exec();

    const utrMap = new Map(utrs.map((utr) => [String(utr._id), utr]));

    return utrvs.map((utrv) => {
      const utr = utrv.universalTracker ?? utrMap.get(utrv.universalTrackerId.toString());
      if (!utr) {
        throw new ContextError('UTR not found when resolve workgroup permission for utrv', {
          utrvId: utrv._id,
          utrId: utrv.universalTrackerId,
        });
      }

      return {
        ...utrv,
        universalTracker: utr,
      };
    });
  }

  private checkIsSurveyWorkgroupPermission({
    permission,
    access,
    roles,
  }: {
    permission: SurveyPermission;
    access: DataScopeAccess;
    roles: SurveyUserRoles[];
  }) {
    return (
      permission.type === SurveyPermissionType.Workgroup &&
      permission.access === access &&
      permission.roles.some((role) => roles.includes(role))
    );
  }
}

let instance: WorkgroupPermissions;

export const getWorkgroupPermissions = () => {
  if (!instance) {
    instance = new WorkgroupPermissions(Workgroup, getSurveyWorkgroupService(), UniversalTracker);
  }
  return instance;
};

/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import ReportDocument, { CreateReportDocument, ReportDocumentPlain } from '../../models/reportDocument';
import { ObjectId } from 'bson';
import { ReportingFactory, getReportingFactory } from '../reporting/ReportingFactory';
import { UserPlain } from '../../models/user';
import { SerializedEditorState } from 'lexical';

export class ReportDocumentManager {
  constructor(private readonly logger: LoggerInterface, private reportingFactory: ReportingFactory) {}

  public async create(createData: CreateReportDocument) {
    const document = new ReportDocument(createData);
    return document.save();
  }

  public async get({ reportId, initiativeId }: { reportId: string; initiativeId: string }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOne({
      _id: new ObjectId(reportId),
      initiativeId: new ObjectId(initiativeId),
    })
      .orFail()
      .lean();
  }

  public async list({ initiativeId }: { initiativeId: string }): Promise<ReportDocumentPlain[]> {
    return ReportDocument.find({
      initiativeId: new ObjectId(initiativeId),
    }).lean();
  }

  public async update({
    reportId,
    initiativeId,
    ...payload
  }: { reportId: string; initiativeId: string; [key: string]: any }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOneAndUpdate(
      {
        _id: new ObjectId(reportId),
        initiativeId: new ObjectId(initiativeId),
      },
      {
        $set: payload,
      },
      { new: true }
    )
      .orFail()
      .exec();
  }

  public async deleteReport({ reportId, initiativeId }: { reportId: string; initiativeId: string }) {
    this.logger.warn('Deleting report document', { reportId, initiativeId });

    return ReportDocument.findOneAndDelete({
      initiativeId: new ObjectId(initiativeId),
      _id: new ObjectId(reportId),
    })
      .orFail()
      .exec();
  }

  public async getTemplate({ reportId, user }: { reportId: string; user: UserPlain }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean();

    const generator = this.reportingFactory.getFactory(reportDocument.type).getLexicalStateGenerator();
    return generator.getTemplate({
      initiativeId: reportDocument.initiativeId,
      user,
      reportType: reportDocument.type,
    });
  }

  public async download({ reportId, editorState }: { reportId: string; editorState: SerializedEditorState }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean();

    const generator = this.reportingFactory.getFactory(reportDocument.type).getLexicalStateGenerator();
    return generator.downloadReport({
      reportDocument,
      editorState,
      reportType: reportDocument.type,
    });
  }
}

let instance: ReportDocumentManager;
export const getReportDocumentManager = () => {
  if (!instance) {
    instance = new ReportDocumentManager(wwgLogger, getReportingFactory());
  }
  return instance;
};

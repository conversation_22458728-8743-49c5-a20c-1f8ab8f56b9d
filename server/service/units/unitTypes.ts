/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema } from "mongoose";
import type { UnitConfig} from '../../types/units';
import { SupportedMeasureUnits, NumberScale, validUnitTypes } from '../../types/units';

// Re-export shared types from centralized location
export {
  SupportedMeasureUnits,
  NumberScale,
  Unit,
  validUnitTypes,
  UnitConfig
} from '../../types/units';

export const numberScaleValueMap: Record<string, number> = {
  [NumberScale.Single]: 0,
  [NumberScale.Hundreds]: 2,
  [NumberScale.Thousands]: 3,
  [NumberScale.Millions]: 6,
  [NumberScale.Billions]: 9,
}

export const blueprintDefaultUnitConfig: UnitConfig = {
  [SupportedMeasureUnits.area]: 'km2',
  [SupportedMeasureUnits.time]: 'h',
  [SupportedMeasureUnits.mass]: 'mt',
  [SupportedMeasureUnits.volume]: 'm3',
  [SupportedMeasureUnits.energy]: 'MWh',
  [SupportedMeasureUnits.currency]: 'USD',
  [SupportedMeasureUnits.co2Emissions]: 'tons/CO2e',
  [SupportedMeasureUnits.partsPer]: 'ppm',
  [SupportedMeasureUnits.numberScale]: NumberScale.Millions,
  [SupportedMeasureUnits.length]: 'km',
};

const definition = validUnitTypes.map((type: SupportedMeasureUnits) => [type, {
  type: Schema.Types.String,
  required: true,
  default: blueprintDefaultUnitConfig[type]
}]);
export const UnitConfigSchema = new Schema(Object.fromEntries(definition), { _id: false });

export const SINGLE_NUMBER_SCALE_VALUES = [NumberScale.Single, undefined, ''];

export const ALLOWED_SIZES = [0, 1];

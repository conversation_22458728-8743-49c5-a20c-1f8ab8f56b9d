/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { getMaterialityAssessmentManager } from '../service/materiality-assessment/MaterialityAssessmentManager';
import { populateInitiative } from '../middleware/commonMiddlewares';
import { createSurveyConfigSchema, createSurveySchema } from './validation-schemas/materiality-assessment';
import { mustValidate } from '../util/validation';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { ObjectId } from 'bson';
import { getBackgroundJobService } from '../service/background-process/BackgroundJobService';
import { MaterialityAssessmentBackgroundJobService } from '../service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import { MaterialityAssessmentService } from '../service/materiality-assessment/MaterialityAssessmentService';
import { AIModelType } from '../service/ai/AIModelFactory';
import { SupportedJobPlain } from '../service/materiality-assessment/background-job/types';
import { getMaterialityAssessmentReportService } from '../service/materiality-assessment/MaterialityAssessmentReportService';
import { FileParserType } from '../service/survey/transfer/parserTypes';
import { write } from '@sheet/core';
import { setCsvFileHeaders, setXlsxFileHeaders } from '../http/FileDownload';
import { UrlMapper } from '../service/url/UrlMapper';
import { canGenerateScore } from '../middleware/materialityAssessmentMiddlewares';
import { canAccessInsights, isMaterialitySurvey, isScoresCalculated } from '../middleware/surveyMiddlewares';
import { z } from 'zod';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { getMaterialityAssessmentConfigService} from '../service/materiality-assessment/MaterialityAssessmentConfigService';
import { SurveyModelPlain } from '../models/survey';
import { AssessmentData, AssessmentResult } from '../service/materiality-assessment/types';
import { UpdateImpactScope } from '../models/materiality';
import { getMTPPTXReportService } from '../service/pptx-report/MTPPTXReportService';

const router = express.Router() as AuthRouter;
const materialityAssessmentManager = getMaterialityAssessmentManager();
const pptxService = getMTPPTXReportService();
const materialityAssessmentReportService = getMaterialityAssessmentReportService();
const backgroundJobService = getBackgroundJobService();
const materialityAssessmentConfigService = getMaterialityAssessmentConfigService();

// this route is only used for MT onboarding flow
router.route('/initiatives/:initiativeId/free-assessment').get(populateInitiative,async (req, res, next) => {
  if (!req.initiative) {
    return next(new PermissionDeniedError());
  }
  const canCreateFreeAssessment = await materialityAssessmentManager.shouldSkipPaymentForFirstAssessment(
    req.initiative,
    req.user
  );
  res.FromModel({ canCreateFreeAssessment });
});

router.route('/initiatives/:initiativeId/surveys').get(async (req, res, next) => {
  try {
    const initiativeId = req.params.initiativeId;
    const surveys = await materialityAssessmentManager.listSurveys(initiativeId);
    res.FromModel(surveys);
  } catch (e) {
    next(e);
  }
});

router
  .route('/initiatives/:initiativeId/context')
  .get(async (req, res, next) => {
    try {
      const utrs = await materialityAssessmentManager.getContextUtrs();

      res.FromModel({
        utrs,
      });
    } catch (e) {
      next(e);
    }
  });

  router
  .route('/initiatives/:initiativeId/create')
  .post(populateInitiative, async (req, res, next) => {
    try {
      if (!req.initiative?._id) {
        return next(new PermissionDeniedError());
      }
      const { effectiveDate, referralCode, returnUrl, assessmentType, verificationRequired } = mustValidate(
        req.body,
        createSurveyConfigSchema
      );
      const context = mustValidate(req.body.answers, createSurveySchema);

      const domain = req.header('origin');
      const returnUrlClean = UrlMapper.relativeUrl(returnUrl, domain);

      const createSurveyData = {
        initiativeId: req.initiative._id,
        user: req.user,
        promoCode: referralCode,
        context,
        metadata: {
          initiativeId: req.initiative._id.toHexString(),
          effectiveDate: effectiveDate.toISOString(),
          userId: req.user._id.toHexString(),
          assessmentType,
          verificationRequired: verificationRequired.toString(),
        },
        returnUrl: returnUrlClean,
      };

      if (await materialityAssessmentManager.shouldSkipPaymentForFirstAssessment(req.initiative, req.user)) {
        const survey = await materialityAssessmentManager.createSurveyWithoutPayment(createSurveyData);
        return res.FromModel(survey);
      }

      const setup = await materialityAssessmentManager.create(createSurveyData);

      res.FromModel(setup);
    } catch (e) {
      next(e);
    }
  });

  router
  .route('/initiatives/:initiativeId/create/:sessionId')
  .get(populateInitiative, async (req, res, next) => {
    try {
      if (!req.initiative?._id) {
        return next(new PermissionDeniedError());
      }

      const results = await materialityAssessmentManager.getSurveyIdBySessionId({
        initiativeId: req.initiative._id,
        sessionId: req.params.sessionId,
      });

      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores/:scoreJobId/questions')
  .get(canAccessInsights, isScoresCalculated, async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      const result = res.locals.scoreJob.tasks[0].data.result as AssessmentResult<AssessmentData>;
      const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
      const utrs = await materialityAssessmentService.getAssessmentMappedUtrs(result);
      res.FromModel(utrs);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores')
  .get(canAccessInsights, canGenerateScore, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;

      const bjService = new MaterialityAssessmentBackgroundJobService(backgroundJobService);
      const job = await bjService.createOrReturnJob({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        userId: req.user._id,
      });

      let result = job.tasks[0].data.result;
      if (result) {
        const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
        result = await materialityAssessmentService.hydrateTopics(result);
      }

      res.FromModel({
        jobId: job._id.toString(),
        status: job.status,
        result: result,
        config: job.tasks[0].data.config,
        updatedAt: job.updated,
      });
    } catch (e) {
      next(e);
    }
  })
  .post(canAccessInsights, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const { modelType = AIModelType.Claude } = req.params;

      const bjService = new MaterialityAssessmentBackgroundJobService(backgroundJobService);
      const job = await bjService.createJob({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        modelType: modelType as AIModelType,
        userId: req.user._id,
      });

      res.FromModel({
        jobId: job._id.toString(),
        status: job.status,
        result: job.tasks[0].data.result,
        updatedAt: job.updated,
      });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/config')
  .put(canManageInitiative, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const { config, impactScopes } = mustValidate(
        req.body,
        z.object({
          config: z.object({
            orderedTopics: z
              .object({
                code: z.string(),
                disabled: z.boolean().optional(),
              })
              .array(),
            explanation: z.string(),
          }),
          impactScopes: z.array(z.nativeEnum(UpdateImpactScope)),
        })
      );

      const result = await materialityAssessmentConfigService.update({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        config,
        userId: req.user._id,
        impactScopes,
      });

      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .delete(canManageInitiative, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const { impactScopes } = mustValidate(
        req.body,
        z.object({ impactScopes: z.array(z.nativeEnum(UpdateImpactScope)) })
      );
      const result = await materialityAssessmentConfigService.update({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        userId: req.user._id,
        impactScopes,
      });
      res.FromModel({ result });
    } catch (e) {
      next(e);
    }
  });

router.route('/initiatives/:initiativeId/surveys/:surveyId/size-scope').get(canAccessInsights, async (req, res, next) => {
  try {
    const { surveyId } = req.params;
    const sizeScope = await materialityAssessmentManager.getSizeScopeByAssessmentId(surveyId);
    res.FromModel({ sizeScope });
  } catch (e) {
    next(e);
  }
});

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores/:scoreJobId/report/pptx')
  .get(canAccessInsights, isMaterialitySurvey, isScoresCalculated, async (req, res) => {
    const { initiativeId } = req.params;
    const survey = res.locals.survey as SurveyModelPlain;
    const existingJob = res.locals.scoreJob as SupportedJobPlain;

    const context = pptxService.getMTReportJobContext({
      userId: req.user._id,
      initiativeId: new ObjectId(initiativeId),
      survey,
      assessmentJob: existingJob,
    });

    const job = await pptxService.createOrReturnJob(context);

    return res.FromModel({
      jobId: job._id.toString(),
      taskId: job.tasks[0].id,
      status: job.status,
      completedDate: job.completedDate,
    });
  })
  .post(canAccessInsights, isMaterialitySurvey, isScoresCalculated, async (req, res, next) => {
    const { initiativeId } = req.params;
    const survey = res.locals.survey as SurveyModelPlain;
    const existingJob = res.locals.scoreJob as SupportedJobPlain;

    const context = pptxService.getMTReportJobContext({
      userId: req.user._id,
      initiativeId: new ObjectId(initiativeId),
      survey,
      assessmentJob: existingJob,
    });

    const job = await pptxService.createJob(context);

    return res.FromModel({
      jobId: job._id.toString(),
      taskId: job.tasks[0].id,
      status: job.status,
    });
  });

['xlsx', 'csv'].forEach((type) => {
router
  .route(`/initiatives/:initiativeId/surveys/:surveyId/metric-answers-report/${type}`)
  .get(async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      const { workbook, filename } = await materialityAssessmentReportService.downloadAssessmentAnswers({ surveyId });
      if (type === 'csv') {
        setCsvFileHeaders(res, `${filename}.${type}`);
      } else {
        setXlsxFileHeaders(res, `${filename}.${type}`);
      }
      return res.send(write(workbook, { type: 'buffer', bookType: type as FileParserType, cellStyles: true }));
    } catch (error) {
      next(error);
    }
  });
})


module.exports = router;

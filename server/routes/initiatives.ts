/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Initiative, { InitiativeModel, InitiativePlain, InitiativeTags, InitiativeTypes, isStaffOrganization, PERMISSION_GROUPS, } from '../models/initiative';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { InitiativeManager } from '../service/initiative/InitiativeManager';
import { populateInitiative, checkPublicPermissions } from '../middleware/commonMiddlewares';
import { DocumentRepository } from '../repository/DocumentRepository';
import { SurveyRepository } from '../repository/SurveyRepository';
import { addDocumentUrl } from '../service/storage/fileStorage';
import { getRatingsRepository, RatingsListQuery, } from '../repository/RatingsRepository';
import { createStandardManager } from '../service/standard/StandardManager';
import { checkIsStaff, } from '../middleware/userMiddlewares';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import ScorecardFactory, { InitiativeWithScorecard } from '../service/scorecard/ScorecardFactory';
import UserError from '../error/UserError';
import InitiativeGroup from '../models/initiativeGroup';
import { ObjectId } from 'bson';
import { clearCache } from '../service/cache';
import { setCsvFileHeaders } from '../http/FileDownload';
import { stringifyArrayCsvFile } from '../service/file/writer/CsvFileWriter';
import { getSurveyDataTransfer } from '../service/survey/SurveyDataTransfer';
import { multiSurveyColumns } from '../service/assurance/csvContext';
import { customDateFormat } from '../util/date';
import { Blueprints } from '../survey/blueprints';
import { AuthRouter } from '../http/AuthRouter';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { UtrAccessFilter } from '../service/utr/UtrAccessFilter';
import { getRootInitiativeService } from '../service/organization/RootInitiativeService';
import { getNotificationManager } from '../service/notification/NotificationManager';
import { wwgLogger } from '../service/wwgLogger';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { canManageInitiative, isRootLevel } from '../middleware/initiativeMiddlewares';
import UniversalTrackerService, { getUniversalTrackerService, ViewType } from '../service/utr/UniversalTrackerService';
import { BankingService } from '../service/banking/BankingService';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { getSurveyManager } from '../service/survey/SurveyManager';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { mustValidate } from '../util/validation';
import { getAvailablePeriodsParamsSchema } from './validation-schemas/insight-dashboards';
import { updateInitiativeTagsSchema } from './validation-schemas/initiatives';
import { getPPTXReportManager } from '../service/pptx-report/PPTXReportManager';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { InitiativeAudit } from '../service/audit/events/Initiative';
import { LEVEL } from '../service/event/Events';
import { Actions } from '../service/action/Actions';
import { Features, FeatureTag } from '@g17eco/core';
import { toBoolean } from '../http/query';
import { getAIDocumentLibraryScanWorkflow } from '../service/ai/document-utr-mapping/AIDocumentUtrMappingWorkflow';
import ContextError from '../error/ContextError';

const router = express.Router() as AuthRouter;

const bc = getBluePrintContribution();
const ratingsRepository = getRatingsRepository();
const standardManager = createStandardManager();
const surveyDataTransfer = getSurveyDataTransfer();
const surveyManager = getSurveyManager();
const auditLogger = getAuditLogger();

router.route('/search/holdings')
  .get((req, res) => {
    InitiativeRepository.searchByHoldings(req.user)
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/search/user')
  .get((req, res) => {
    InitiativeRepository.searchByUserTree(req.query.q as string, req.user)
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/search/organization')
  .get((req, res) => {
    const isUserStaff = req.user.isStaff;

    InitiativeRepository.findWithTags([InitiativeTags.Organization], false, true)
      .then((initiatives) => {
        const filteredInitiatives = isUserStaff ? initiatives : initiatives.filter(i => !isStaffOrganization(i));
        return res.FromModel(filteredInitiatives);
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/ratings/agencies')
  .get((req, res) => {
    ratingsRepository.ratingAgencies()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:initiativeId')
  .get(checkPublicPermissions, (req, res) => {
    const initiative = req.initiative as InitiativePlain;
    // Do we still need to return documents when querying initiative?
    // TODO: Remove documents in release 4.27
    DocumentRepository.findInitiativeDocuments(initiative._id)
      .then(async (data) => {
        const { documents } = await addDocumentUrl({ documents: data });
        initiative.files = documents;
        const permissions = await UserInitiativeRepository.getInitiativeUserPermissions(req.user, String(initiative._id));
        res.FromModel({
          ...initiative,
          userPermissions: permissions,
          bankingSettings: BankingService.getBanks(initiative.bankingSettings)
        });
      }).catch((e: Error) => res.Exception(e));
  });

router.route('/:initiativeId/beta-features-tags')
  .get(canManageInitiative, async (req, res, next) => {
    try {
      const { configTags, defaultTags } = await InitiativeManager.getDefaultAndConfigBetaFeatureTags(
        req.params.initiativeId
      );

      res.FromModel({ configTags, defaultTags });
    } catch (error) {
      return next(error);
    }
  })
  .patch(canManageInitiative, isRootLevel, async (req, res, next) => {
    try {
      const data = mustValidate(req.body, updateInitiativeTagsSchema);
      // Must be root organization due to middleware check
      const rootOrg = res.locals.initiative as InitiativeModel;
      const orgTags = rootOrg.tags?.filter((tag) => tag !== data.tag) ?? [];

      switch (data.action) {
        case Actions.Add:
          rootOrg.tags = [...orgTags, data.tag];
          break;
        case Actions.Remove:
          rootOrg.tags = orgTags;
          break;
        default:
          throw new Error('Invalid action');
      }

      const updatedRootOrg = await rootOrg.save();

      if (data.tag === FeatureTag.AIAccessDocumentLibrary && data.action === Actions.Add) {
        getAIDocumentLibraryScanWorkflow().upsertJob(updatedRootOrg._id, req.user._id).catch(wwgLogger.error);
      }

      if (toBoolean(req.query.isAccepted)) {
        auditLogger
          .fromRequest(req, {
            initiativeId: rootOrg._id,
            auditEvent: InitiativeAudit.aiFeaturesAccepted,
            targets: [auditLogger.initiativeTarget(rootOrg)],
            debugData: data,
            message: InitiativeAudit.aiFeaturesAccepted.description,
          })
          .catch(wwgLogger.error);
      }

      auditLogger
        .fromRequest(req, {
          initiativeId: rootOrg._id,
          auditEvent: InitiativeAudit.featuresUpdate,
          severity: LEVEL.INFO,
          targets: [auditLogger.initiativeTarget(rootOrg)],
          debugData: data,
          message: `${Features[data.tag].name} switched ${data.action === Actions.Add ? 'on' : 'off'}`,
        })
        .catch(wwgLogger.error);

      return res.FromModel(data);
    } catch (error) {
      return next(error);
    }
  });

router.route('/:initiativeId/blueprint/:code/questions').get(canManageInitiative, async (req, res) => {
  const initiative = await UserInitiativeRepository.getUserInitiative(req.user, req.params.initiativeId);
  if (!initiative) {
    throw new ContextError('No initiative');
  }

  const utrs = await bc.getQuestionsWithCustomMetrics(req.params.code as Blueprints, req.params.initiativeId);

  const domain = req.header('origin');

  const rootInitiativeService = getRootInitiativeService();
  const domainConfig = await rootInitiativeService.getDomainConfig({ domain });
  const config = await rootInitiativeService.getConfig(initiative, { domain });

  const allowAll = domainConfig?.customMetric?.allowAll;
  const filteredUtrs = allowAll ? utrs : UtrAccessFilter.verifyAccess(utrs, config);

  return res.FromModel(filteredUtrs);
});

router.route('/:initiativeId/export/csv')
  .get(checkPublicPermissions, canManageInitiative, async (req, res) => {
    try {
      const initiative = req.initiative as InitiativePlain;
      const surveys = await SurveyRepository.findSurveys({
        initiativeId: initiative._id,
        deletedDate: { $exists: false },
      }, { visibleUtrvs: 1 });

      const date = customDateFormat(new Date());
      const filename = `G17Eco ${initiative.name} - all reports data (${date}).csv`;

      const result = await surveyDataTransfer.createCsvForUtrvs({
        utrvIds: surveys.map(s => s.visibleUtrvs).flat(),
        user: req.user,
        filename,
        columnSetup: multiSurveyColumns,
      });

      setCsvFileHeaders(res, result.filename);
      return res.send(stringifyArrayCsvFile(result));
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:initiativeId/ratings')
  .get(checkPublicPermissions, async (req, res) => {
    try {
      const query = new RatingsListQuery(req.params.initiativeId);
      const results = await ratingsRepository.list(query);
      res.FromModel(results);
    } catch (e) {
      res.Exception(e);
    }
  })
  .patch(canManageInitiative, (req, res) => {
    Initiative.findById(req.params.initiativeId).orFail().exec()
      .then(initiative => InitiativeManager.updateRatings(initiative, req.body.ratings))
      .then((initiative) => res.FromModel(initiative))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:initiativeId/ratings/:code{/:id}')
  .patch(canManageInitiative, (req, res) => {

    const { code, id } = req.params;
    const rating = req.body.rating;
    if (code !== rating?.code) {
      throw new Error(`Route parameter "${code}" does not match body "${rating?.code}"`)
    }

    if (id !== rating?._id) {
      throw new Error(`Route parameter "${id}" does not match body "${rating?.id}"`)
    }

    Initiative.findById(req.params.initiativeId).orFail().exec()
      .then(initiative => InitiativeManager.editRating(initiative, rating))
      .then((initiative) => res.FromModel(initiative))
      .catch((e: Error) => res.Exception(e));
  })
  .delete(canManageInitiative, (req, res) => {
    const { code, initiativeId, id } = req.params;
    Initiative.findById(initiativeId).orFail().exec()
      .then(initiative => {
        return id ?
          InitiativeManager.deleteRatingById(initiative, id) :
          InitiativeManager.deleteRating(initiative, code);
      })
      .then((initiative) => res.FromModel(initiative))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:initiativeId/standards')
  .get(checkPublicPermissions, (req, res) => {
    standardManager.getStandardByInitiative(req.initiative as InitiativePlain, req.user)
      .then(r => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

router.route('/:initiativeId/standards/:surveyId')
  .get(checkPublicPermissions, (req, res) => {
    SurveyRepository.mustFindById(req.params.surveyId)
      .then(survey => standardManager.getSurveyStandards(survey))
      .then(r => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

router.route('/:initiativeId/verified-count/:surveyId')
  .get(checkPublicPermissions, (req, res) => {
    SurveyRepository.mustFindById(req.params.surveyId)
      .then(survey => standardManager.getSurveyVerifiedByGroup(survey))
      .then(r => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

router.route('/:initiativeId/portfolios')
  .get(populateInitiative, async (req, res) => {
    const initiative = req.initiative as InitiativePlain;
    try {
      const portfolios = await Initiative
        .find({ parentId: initiative._id, type: InitiativeTypes.Group })
        .sort({ name: 1 })
        .lean<InitiativeWithScorecard[]>();

      const scorecardFactory = new ScorecardFactory();
      for (const portfolio of portfolios) {
        portfolio.scorecard = await scorecardFactory.getByInitiativeGroupId(portfolio.initiativeGroupId);
      }

      res.FromModel(portfolios);
    } catch (e) {
      res.Exception(e);
    }
  })
  .post(populateInitiative, canManageInitiative, async (req, res) => {
    const initiative = req.initiative as InitiativePlain;
    if (!initiative.initiativeGroupId) {
      return res.Exception(new UserError('This is not a valid reporting level to create a portfolio'));
    }

    try {
      const initiativeGroup = await InitiativeGroup.findById(initiative.initiativeGroupId).orFail().exec();

      const id = new ObjectId();
      const newCode = `${initiative.code}/${id}`;

      const portfolioGroup = new InitiativeGroup({
        code: newCode,
        name: `${req.body.name} Group`,
        group: []
      });
      await portfolioGroup.save();

      const portfolio = new Initiative({
        code: newCode,
        name: req.body.name,
        type: InitiativeTypes.Group,
        initiativeGroupId: portfolioGroup._id,
        parentId: initiative._id
      });
      await portfolio.save();

      initiativeGroup.group.push({ initiativeId: portfolio._id, weight: 0 });
      await initiativeGroup.save();
      clearCache();
      res.FromModel(portfolio);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:initiativeId/portfolio')
  .get(checkPublicPermissions, async (req, res) => {
    try {
      const initiative = req.initiative as InitiativePlain;
      if (!initiative.initiativeGroupId) {
        throw new UserError('Not a valid Portfolio');
      }
      const scorecardFactory = new ScorecardFactory();
      const scorecard = await scorecardFactory.getByInitiativeGroupId(initiative.initiativeGroupId);
      res.FromModel({
        ...initiative,
        scorecard
      });
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:initiativeId/surveys')
  .get(populateInitiative, async (req, res) => {
    const initiative = req.initiative as InitiativePlain;
    try {
      const surveyActions = await SurveyRepository.getSurveyStatsByInitiativeId([initiative._id]);

      const scorecardFactory = new ScorecardFactory();
      const actionWithScorecard = await Promise.all(surveyActions.map(async (action) => ({
        ...action,
        initiative, // Override initiative with inherited properties
        scorecard: await scorecardFactory.getGoalScoresBySurveyId(action._id),
      })));

      return res.FromModel(actionWithScorecard);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:initiativeId/surveys/children')
  .get(populateInitiative, async (req, res) => {
    const initiative = req.initiative as InitiativePlain;
    try {
      const childInitiatives = await Initiative.find({ parentId: initiative._id }, { _id: 1 }).lean().exec();
      const surveyActions = await SurveyRepository.getSurveyStatsByInitiativeId(childInitiatives.map(i => i._id));

      return res.FromModel(surveyActions);
    } catch (e) {
      res.Exception(e);
    }
  });

['true', 'false'].forEach(value => {

router.route(`/:initiativeId/verified/${value}`)
  .patch(checkIsStaff, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId; // Parsed by middleware
      const i = await Initiative.findById(initiativeId).exec();

      if (!i) {
        return res.Exception(`Invalid Initiative ID ${initiativeId}`);
      }

      i.tags = i.tags?.filter(tag => tag !== InitiativeTags.VerifiedOrganization);

      // TEMP AS WE DEPRECATE THIS TAG. Never made it to prod, just tidying up for dev/staging
      i.tags = i.tags?.filter(tag => tag !== 'unverified_organization');

      if (value === 'true') {
        i.tags?.push(InitiativeTags.VerifiedOrganization);
      }

      await i.save();
      res.Success('Successfully updated document with _id=' + initiativeId)
      clearCache();
    } catch (e) { next(e); }
  });
})

router.route('/:initiativeId/premium')
  .patch(checkIsStaff, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId; // Parsed by middleware
      const i = await Initiative.findById(initiativeId).exec();
      if (!i) {
        return res.Exception(`Invalid Initiative ID "${initiativeId}"`);
      }

      if (!i.permissionGroup || i.permissionGroup === PERMISSION_GROUPS.FREE) {
        i.permissionGroup = PERMISSION_GROUPS.COMPANY_TRACKER_PRO
        await i.save();
      }
      res.Success(`Successfully updated company with _id=${initiativeId}`)
      clearCache();
    } catch (e) {
      next(e);
    }
  });

router.route('/:initiativeId/universal-trackers/code/:utrCode/:period')
  .get(populateInitiative, async (req, res) => {
    try {
      const { initiativeId, utrCode, period } = req.params;
      const insightsData = await UniversalTrackerService.getInsightsData(initiativeId, utrCode, period);
      res.FromModel(insightsData);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:initiativeId/notifications/send').post(populateInitiative, ContextMiddleware, async (req, res, next) => {
  try {
    const fromUserId = String(req.user._id);
    const { initiativeId } = req.params;
    await getNotificationManager()
      .sendUserGeneratedNotification(fromUserId, { ...req.body, initiativeId })
      .catch(wwgLogger.error);
    res.Success();
  } catch (e) {
    next(e);
  }
});

router.route('/:initiativeId/surveys-periods{/:surveyType}')
  .get(async function (req: any, res, next) {
    try {
      const { initiativeId, surveyType } = mustValidate(req.params, getAvailablePeriodsParamsSchema);
      const user = req.user;
      const canAccess = await InitiativePermissions.canAccessAllSurveyData(user, initiativeId);
      if (!canAccess) {
        return next(new PermissionDeniedError());
      }
      const periods = await surveyManager.getUniquePeriods(initiativeId, surveyType);
      return res.FromModel(periods);
    } catch (e) {
      next(e);
    }
  });

Object.values(ViewType).forEach(viewType => {

router.route(`/:initiativeId/questions/${viewType}`).get(canManageInitiative, (req, res, next) => {
  const initiativeId = res.locals.initiativeId;

  getUniversalTrackerService().getUniversalTrackersByInitiativeId(initiativeId, viewType)
    .then((result) => {
      res.FromModel(result);
    })
    .catch((e: Error) => next(e));
});
});


router.route('/:initiativeId/reports/pptx').get(populateInitiative, async (req, res, next) => {
  try {
    const initiativeId = req.params.initiativeId;
    const reports = await getPPTXReportManager().getListReports({ initiativeId, user: req.user });
    res.FromModel(reports);
  } catch (e) {
    next(e);
  }
});

module.exports = router;

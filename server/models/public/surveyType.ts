import { ObjectId } from "bson";
import type { UniversalTrackerValuePublic, StakeholderGroup } from "./universalTrackerValueType";
import { ObjectIdString } from './objectId';

export interface Roles<T = ObjectId> {
  viewer: T[];
  admin: T[];
}

export interface UnitConfigMap {
  [key: string]: string
}

export interface SurveyPublic<T = ObjectIdString> {
  _id: T;
  name?: string;
  code: string;
  initiativeId: T;
  effectiveDate: Date;
  universalTrackerValues: UniversalTrackerValuePublic<T>[];
  stakeholders: StakeholderGroup;
  unitConfig: UnitConfigMap;
  roles?: Roles;
}

export interface SurveyStakeholderRoles {
  _id: ObjectId;
  roles: Roles;
  stakeholders: StakeholderGroup;
}
